<?php
/**
 * Fetch Categories Script
 * 
 * This script fetches categories from a WordPress site via the REST API.
 */

// Enable error reporting
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

// Set content type to JSON
header('Content-Type: application/json');

// Load configuration
require_once 'config.php';

// Check if URL is provided
if (!isset($_GET['url']) || empty($_GET['url'])) {
    echo json_encode([
        'success' => false,
        'error' => 'URL is required'
    ]);
    exit;
}

$url = $_GET['url'];

// Validate URL
if (!filter_var($url, FILTER_VALIDATE_URL)) {
    echo json_encode([
        'success' => false,
        'error' => 'Invalid URL'
    ]);
    exit;
}

// Function to fetch data from URL
function fetchUrl($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // Set user agent
    $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
    
    // Set headers
    $headers = [
        'Accept: application/json',
        'Cache-Control: no-cache',
        'Pragma: no-cache'
    ];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch) || $httpCode < 200 || $httpCode >= 300) {
        curl_close($ch);
        return false;
    }
    
    curl_close($ch);
    return $response;
}

// Build the WordPress REST API URL for categories
$apiUrl = rtrim($url, '/') . '/wp-json/wp/v2/categories?per_page=100';

// Try to fetch categories
$response = fetchUrl($apiUrl);

if ($response === false) {
    // Try alternative URL formats
    $alternativeUrls = [
        rtrim($url, '/') . '/wp-json/wp/v2/categories',
        rtrim($url, '/') . '/index.php/wp-json/wp/v2/categories',
        rtrim($url, '/') . '/index.php?rest_route=/wp/v2/categories'
    ];
    
    foreach ($alternativeUrls as $altUrl) {
        $response = fetchUrl($altUrl);
        if ($response !== false) {
            break;
        }
    }
    
    if ($response === false) {
        echo json_encode([
            'success' => false,
            'error' => 'Failed to fetch categories from WordPress API'
        ]);
        exit;
    }
}

// Parse the response
$categories = json_decode($response, true);

if (!is_array($categories)) {
    echo json_encode([
        'success' => false,
        'error' => 'Invalid response from WordPress API'
    ]);
    exit;
}

// Filter and format categories
$formattedCategories = [];
foreach ($categories as $category) {
    // Skip the "Uncategorized" category (usually ID 1)
    if ($category['id'] == 1 && $category['slug'] == 'uncategorized') {
        continue;
    }
    
    // Only include categories with posts
    if ($category['count'] > 0) {
        $formattedCategories[] = [
            'id' => $category['id'],
            'name' => $category['name'],
            'slug' => $category['slug'],
            'count' => $category['count'],
            'description' => $category['description'] ?? '',
            'parent' => $category['parent'] ?? 0
        ];
    }
}

// Sort categories by post count (descending)
usort($formattedCategories, function($a, $b) {
    return $b['count'] - $a['count'];
});

// Return the categories
echo json_encode([
    'success' => true,
    'categories' => $formattedCategories
]);
exit;
?>
