<?php
/**
 * Fix Database Schema
 *
 * This script updates the database schema to ensure it has all required columns.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// Set page title
$pageTitle = 'Fix Database Schema';

// Include tool header
include_once 'includes/tool_header.php';

// Add custom styles
?>
<style>
    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow: auto;
        font-size: 14px;
        line-height: 1.5;
    }
    .success {
        color: green;
        font-weight: bold;
    }
    .error {
        color: red;
        font-weight: bold;
    }
    .warning {
        color: orange;
        font-weight: bold;
    }
</style>
<?php

// Function to check if a column exists in a table
function columnExists($db, $table, $column) {
    try {
        $db->query("SELECT $column FROM $table LIMIT 1");
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Check if the is_future column exists in the posts table
$isFutureExists = columnExists($db, 'posts', 'is_future');

echo '<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Database Schema Check</h5>
    </div>
    <div class="card-body">
        <pre>';

echo "Checking database schema...\n\n";

echo "Table: posts\n";
echo "Column: is_future - " . ($isFutureExists ? "<span class='success'>Exists</span>" : "<span class='warning'>Missing</span>") . "\n\n";

// If the is_future column doesn't exist, add it
if (!$isFutureExists) {
    if (isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
        try {
            // Add is_future column to posts table (ALTER TABLE can't be part of a transaction in many databases)
            $db->query("ALTER TABLE posts ADD COLUMN is_future TINYINT(1) NOT NULL DEFAULT 0");

            // Start transaction for the UPDATE
            $db->beginTransaction();

            // Update existing posts to set is_future based on date_published
            $db->query("UPDATE posts SET is_future = IF(date_published > NOW(), 1, 0)");

            // Commit transaction
            $db->commit();

            echo "<span class='success'>Successfully added is_future column to posts table!</span>\n";
            echo "All existing posts have been updated to set is_future based on their publication date.\n";

            // Verify the column was added
            $isFutureExists = columnExists($db, 'posts', 'is_future');
            echo "Column: is_future - " . ($isFutureExists ? "<span class='success'>Exists</span>" : "<span class='error'>Still missing</span>") . "\n";
        } catch (Exception $e) {
            // Rollback transaction if active
            try {
                if ($db->inTransaction()) {
                    $db->rollback();
                }
            } catch (Exception $rollbackError) {
                // Ignore rollback errors
            }

            echo "<span class='error'>Error adding is_future column: " . $e->getMessage() . "</span>\n";
        }
    } else {
        echo "The is_future column needs to be added to the posts table.\n";
        echo "This column is used to track posts with future publication dates.\n\n";

        echo "Click the button below to update the database schema:\n";
        echo '</pre>
        <form method="post" class="mt-3">
            <input type="hidden" name="confirm" value="yes">
            <button type="submit" class="btn btn-primary">Update Database Schema</button>
        </form>';
    }
} else {
    echo "<span class='success'>Database schema is up to date!</span>\n";
    echo "The is_future column already exists in the posts table.\n";
    echo '</pre>';
}

echo '</div>
</div>';

// Include tool footer
include_once 'includes/tool_footer.php';
?>
