<?php
/**
 * Test AutoGptGrabber
 *
 * This script tests the specialized AutoGptGrabber class.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Create content directories
create_content_directories();

// Load required files
require_once 'includes/Database.php';
require_once 'includes/ContentGrabber.php';
require_once 'includes/AutoGptGrabber.php';

// Initialize database connection
$db = new Database();

// URL to test
$url = 'https://autogpt.net';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 5;
$disable_embed = isset($_GET['disable_embed']) ? (bool)$_GET['disable_embed'] : false;
$debug = true;

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AutoGptGrabber</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow: auto;
            max-height: 400px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .warning {
            color: orange;
            font-weight: bold;
        }
        .info {
            color: blue;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1>Test AutoGptGrabber</h1>
        <p class="lead">This tool tests the specialized AutoGptGrabber class.</p>

        <form method="get" class="mb-4">
            <div class="mb-3">
                <label for="limit" class="form-label">Number of posts to grab</label>
                <input type="number" class="form-control" id="limit" name="limit" value="' . $limit . '" min="1" max="100">
            </div>
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="disable_embed" name="disable_embed" value="1" ' . ($disable_embed ? 'checked' : '') . '>
                <label class="form-check-label" for="disable_embed">Disable _embed parameter</label>
            </div>
            <button type="submit" class="btn btn-primary">Test Grabber</button>
        </form>

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Test Results</h5>
            </div>
            <div class="card-body">
                <pre>';

echo "<span class='info'>Testing AutoGptGrabber</span>\n";
echo "URL: $url\n";
echo "Limit: $limit posts\n";
echo "Disable _embed: " . ($disable_embed ? 'Yes' : 'No') . "\n";
echo "Debug mode: Enabled\n\n";

// Create a custom error handler to capture all errors
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    echo "<span class='error'>PHP ERROR [$errno]: $errstr in $errfile on line $errline</span>\n";
    return true;
});

try {
    // Create grabber with debug mode
    echo "<span class='info'>Creating AutoGptGrabber...</span>\n";
    $options = [
        'debug' => $debug,
        'disable_embed' => $disable_embed
    ];

    // Create grabber
    $grabber = new AutoGptGrabber($db, $url, $options);

    // Grab posts
    echo "<span class='info'>Grabbing $limit posts...</span>\n";
    $startTime = microtime(true);
    $posts = $grabber->grab($limit);
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);

    if (empty($posts)) {
        echo "<span class='error'>No posts grabbed!</span>\n";
        echo "Execution time: $executionTime seconds\n\n";
    } else {
        echo "<span class='success'>Successfully grabbed " . count($posts) . " posts in $executionTime seconds!</span>\n\n";

        // Display post details
        echo "<strong>Post details:</strong>\n";
        echo "------------\n";
        foreach ($posts as $index => $post) {
            echo "Post #" . ($index + 1) . ":\n";
            echo "  ID: " . $post['id'] . "\n";
            echo "  Title: " . $post['title'] . "\n";
            echo "  Date: " . $post['date'] . "\n";
            echo "  Is future: " . ($post['is_future'] ? 'Yes' : 'No') . "\n";
            echo "  Link: " . $post['link'] . "\n";
            echo "  Content length: " . strlen($post['content']) . " bytes\n";
            echo "  Categories: " . count($post['categories']) . "\n";
            echo "  Tags: " . count($post['tags']) . "\n";
            echo "\n";
        }

        // Save posts to database
        echo "<strong>Saving posts to database:</strong>\n";
        echo "------------\n";

        // Create a job for testing
        $jobId = $db->query("SELECT id FROM jobs WHERE url = ?", [$url]);
        if (empty($jobId)) {
            // Check the structure of the jobs table
            $tableInfo = $db->query("DESCRIBE jobs");
            $hasFrequencyColumn = false;

            foreach ($tableInfo as $column) {
                if ($column['Field'] === 'frequency') {
                    $hasFrequencyColumn = true;
                    break;
                }
            }

            // Create job data based on table structure
            $jobData = [
                'name' => 'AutoGPT.net Test',
                'url' => $url,
                'type' => 'wordpress',
                'status' => 'active',
                'posts_per_run' => $limit,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Add frequency column if it exists
            if ($hasFrequencyColumn) {
                $jobData['frequency'] = 'daily';
            }

            // Add last_run column if it exists
            $hasLastRunColumn = false;
            foreach ($tableInfo as $column) {
                if ($column['Field'] === 'last_run') {
                    $hasLastRunColumn = true;
                    break;
                }
            }

            if ($hasLastRunColumn) {
                $jobData['last_run'] = null;
            }

            $jobId = $db->insert('jobs', $jobData);
            echo "Created new job with ID: $jobId\n";
        } else {
            $jobId = $jobId[0]['id'];
            echo "Using existing job with ID: $jobId\n";
        }

        // Save posts
        $savedCount = 0;
        foreach ($posts as $post) {
            // Check if post already exists
            $existingPost = $db->query("SELECT id FROM posts WHERE job_id = ? AND external_id = ?", [
                $jobId,
                $post['id']
            ]);

            if (!empty($existingPost)) {
                $postId = $existingPost[0]['id'];
                $db->update('posts', [
                    'title' => $post['title'],
                    'content' => $post['content'],
                    'excerpt' => $post['excerpt'],
                    'date_published' => date('Y-m-d H:i:s', strtotime($post['date'])),
                    'date_modified' => date('Y-m-d H:i:s', strtotime($post['modified'])),
                    'featured_image' => $post['featured_image'] ? json_encode($post['featured_image']) : null,
                    'html_file' => HTML_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.html',
                    'pdf_file' => PDF_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.pdf',
                    'is_future' => $post['is_future'] ? 1 : 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$postId]);
                echo "Updated post: " . $post['title'] . "\n";
            } else {
                $postId = $db->insert('posts', [
                    'job_id' => $jobId,
                    'external_id' => $post['id'],
                    'title' => $post['title'],
                    'content' => $post['content'],
                    'excerpt' => $post['excerpt'],
                    'url' => $post['link'],
                    'slug' => $post['slug'],
                    'date_published' => date('Y-m-d H:i:s', strtotime($post['date'])),
                    'date_modified' => date('Y-m-d H:i:s', strtotime($post['modified'])),
                    'featured_image' => $post['featured_image'] ? json_encode($post['featured_image']) : null,
                    'html_file' => HTML_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.html',
                    'pdf_file' => PDF_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.pdf',
                    'is_future' => $post['is_future'] ? 1 : 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                echo "Inserted new post: " . $post['title'] . "\n";
            }
            $savedCount++;
        }

        echo "<span class='success'>Successfully saved $savedCount posts to database!</span>\n";
    }
} catch (Exception $e) {
    echo "<span class='error'>Error: " . $e->getMessage() . "</span>\n";
    echo "File: " . $e->getFile() . " on line " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Restore error handler
restore_error_handler();

echo '</pre>
            </div>
        </div>

        <div class="mt-4">
            <a href="web_run_job.php" class="btn btn-secondary">Back to Web Job Runner</a>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
?>
