<?php
/**
 * WordPress API Test
 * 
 * This script tests the WordPress API directly to diagnose issues.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// URL to test
$url = isset($_GET['url']) ? $_GET['url'] : 'https://autogpt.net';

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress API Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow: auto;
            max-height: 400px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .warning {
            color: orange;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1>WordPress API Test</h1>
        <p class="lead">This tool tests the WordPress API directly to diagnose issues.</p>
        
        <form method="get" class="mb-4">
            <div class="input-group">
                <input type="url" class="form-control" name="url" value="' . htmlspecialchars($url) . '" placeholder="Enter WordPress URL" required>
                <button type="submit" class="btn btn-primary">Test API</button>
            </div>
        </form>';

if (!empty($url)) {
    echo '<div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Testing WordPress API for: ' . htmlspecialchars($url) . '</h5>
        </div>
        <div class="card-body">
            <pre>';
    
    // Function to fetch URL
    function fetchUrl($url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        $response = curl_exec($ch);
        $info = curl_getinfo($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        return [
            'response' => $response,
            'info' => $info,
            'error' => $error
        ];
    }
    
    // Test main site
    echo "Testing main site...\n";
    $mainResult = fetchUrl($url);
    
    if ($mainResult['info']['http_code'] >= 200 && $mainResult['info']['http_code'] < 300) {
        echo "<span class='success'>Main site is accessible! ✓</span>\n";
        echo "HTTP Code: " . $mainResult['info']['http_code'] . "\n";
        echo "Content Type: " . $mainResult['info']['content_type'] . "\n";
        echo "Response Size: " . $mainResult['info']['size_download'] . " bytes\n\n";
    } else {
        echo "<span class='error'>Main site is not accessible! ✗</span>\n";
        echo "HTTP Code: " . $mainResult['info']['http_code'] . "\n";
        echo "Error: " . $mainResult['error'] . "\n\n";
    }
    
    // Test WordPress API
    echo "Testing WordPress API...\n";
    $apiUrl = rtrim($url, '/') . '/wp-json/';
    $apiResult = fetchUrl($apiUrl);
    
    if ($apiResult['info']['http_code'] >= 200 && $apiResult['info']['http_code'] < 300) {
        echo "<span class='success'>WordPress API is accessible! ✓</span>\n";
        echo "HTTP Code: " . $apiResult['info']['http_code'] . "\n";
        echo "Content Type: " . $apiResult['info']['content_type'] . "\n";
        echo "Response Size: " . $apiResult['info']['size_download'] . " bytes\n\n";
        
        // Check if it's valid JSON
        $apiData = json_decode($apiResult['response'], true);
        if ($apiData !== null) {
            echo "<span class='success'>API response is valid JSON! ✓</span>\n";
            
            // Check if it has the wp/v2 namespace
            if (isset($apiData['namespaces']) && in_array('wp/v2', $apiData['namespaces'])) {
                echo "<span class='success'>API has wp/v2 namespace! ✓</span>\n\n";
            } else {
                echo "<span class='error'>API does not have wp/v2 namespace! ✗</span>\n\n";
            }
        } else {
            echo "<span class='error'>API response is not valid JSON! ✗</span>\n\n";
        }
    } else {
        echo "<span class='error'>WordPress API is not accessible! ✗</span>\n";
        echo "HTTP Code: " . $apiResult['info']['http_code'] . "\n";
        echo "Error: " . $apiResult['error'] . "\n\n";
    }
    
    // Test posts endpoint
    echo "Testing posts endpoint...\n";
    $postsUrl = rtrim($url, '/') . '/wp-json/wp/v2/posts?per_page=1';
    $postsResult = fetchUrl($postsUrl);
    
    if ($postsResult['info']['http_code'] >= 200 && $postsResult['info']['http_code'] < 300) {
        echo "<span class='success'>Posts endpoint is accessible! ✓</span>\n";
        echo "HTTP Code: " . $postsResult['info']['http_code'] . "\n";
        echo "Content Type: " . $postsResult['info']['content_type'] . "\n";
        echo "Response Size: " . $postsResult['info']['size_download'] . " bytes\n\n";
        
        // Check if it's valid JSON
        $postsData = json_decode($postsResult['response'], true);
        if ($postsData !== null) {
            echo "<span class='success'>Posts response is valid JSON! ✓</span>\n";
            
            // Check if it has posts
            if (is_array($postsData) && !empty($postsData)) {
                echo "<span class='success'>Found " . count($postsData) . " posts! ✓</span>\n\n";
                
                // Display post details
                $post = $postsData[0];
                echo "Post details:\n";
                echo "- ID: " . $post['id'] . "\n";
                echo "- Title: " . $post['title']['rendered'] . "\n";
                echo "- Date: " . $post['date'] . "\n";
                echo "- Status: " . $post['status'] . "\n";
                echo "- Link: " . $post['link'] . "\n\n";
                
                // Check if the post date is in the future
                $postDate = strtotime($post['date']);
                $now = time();
                if ($postDate > $now) {
                    echo "<span class='warning'>This post has a future date! (" . date('Y-m-d H:i:s', $postDate) . ")</span>\n";
                    echo "Current time: " . date('Y-m-d H:i:s', $now) . "\n";
                    echo "Time difference: " . ($postDate - $now) . " seconds (" . round(($postDate - $now) / 86400) . " days)\n\n";
                }
                
                // Check total posts
                $totalPosts = $postsResult['info']['http_code'] === 200 && isset($postsResult['info']['header_size']) ? 
                    intval($postsResult['info']['header_size']) : 0;
                echo "Total posts available: " . $totalPosts . "\n\n";
                
                // Get total posts from headers
                if (isset($postsResult['info']['header_size'])) {
                    $headers = substr($postsResult['response'], 0, $postsResult['info']['header_size']);
                    if (preg_match('/X-WP-Total: (\d+)/', $headers, $matches)) {
                        echo "X-WP-Total header: " . $matches[1] . " posts\n";
                    }
                    if (preg_match('/X-WP-TotalPages: (\d+)/', $headers, $matches)) {
                        echo "X-WP-TotalPages header: " . $matches[1] . " pages\n\n";
                    }
                }
            } else {
                echo "<span class='error'>No posts found! ✗</span>\n\n";
            }
        } else {
            echo "<span class='error'>Posts response is not valid JSON! ✗</span>\n\n";
        }
    } else {
        echo "<span class='error'>Posts endpoint is not accessible! ✗</span>\n";
        echo "HTTP Code: " . $postsResult['info']['http_code'] . "\n";
        echo "Error: " . $postsResult['error'] . "\n\n";
    }
    
    // Test with different parameters
    echo "Testing with different parameters...\n";
    $testParams = [
        'Default' => '/wp-json/wp/v2/posts?per_page=1',
        'With _embed' => '/wp-json/wp/v2/posts?per_page=1&_embed=1',
        'With status' => '/wp-json/wp/v2/posts?per_page=1&status=publish,future',
        'With orderby date' => '/wp-json/wp/v2/posts?per_page=1&orderby=date&order=desc',
        'With after date' => '/wp-json/wp/v2/posts?per_page=1&after=2020-01-01T00:00:00Z',
        'With before date' => '/wp-json/wp/v2/posts?per_page=1&before=2030-01-01T00:00:00Z'
    ];
    
    foreach ($testParams as $name => $param) {
        $testUrl = rtrim($url, '/') . $param;
        echo "Testing $name ($testUrl)...\n";
        $testResult = fetchUrl($testUrl);
        
        if ($testResult['info']['http_code'] >= 200 && $testResult['info']['http_code'] < 300) {
            echo "<span class='success'>Request successful! ✓</span>\n";
            $testData = json_decode($testResult['response'], true);
            if ($testData !== null && is_array($testData) && !empty($testData)) {
                echo "<span class='success'>Found " . count($testData) . " posts! ✓</span>\n\n";
            } else {
                echo "<span class='error'>No posts found or invalid JSON! ✗</span>\n\n";
            }
        } else {
            echo "<span class='error'>Request failed! ✗</span>\n";
            echo "HTTP Code: " . $testResult['info']['http_code'] . "\n\n";
        }
    }
    
    echo '</pre>
        </div>
    </div>';
}

echo '</div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
?>
