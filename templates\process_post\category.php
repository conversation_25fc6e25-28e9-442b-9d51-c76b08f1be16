<div class="row">
    <div class="col-md-6">
        <h4 class="mb-3">Original Categories</h4>
        <div class="card mb-4">
            <div class="card-body">
                <?php
                // Get categories
                $categories = $db->query("
                    SELECT c.name 
                    FROM post_categories pc
                    JOIN categories c ON pc.category_id = c.id
                    WHERE pc.post_id = ?
                ", [$post['id']]);
                
                if (!empty($categories)):
                ?>
                <div>
                    <?php foreach ($categories as $category): ?>
                    <span class="badge bg-primary me-1 mb-1"><?php echo htmlspecialchars($category['name']); ?></span>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <p class="text-muted mb-0">No categories assigned to this post.</p>
                <?php endif; ?>
            </div>
        </div>
        
        <h4 class="mb-3">Process Categories</h4>
        <div class="card mb-4">
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="process_categories">
                    
                    <p class="text-muted">Generate relevant categories for this post based on its content.</p>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-folder me-1"></i> Generate Categories
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <h4 class="mb-3">Generated Categories</h4>
        
        <?php if ($processedPost && !empty($processedPost['processed_categories'])): ?>
        <div class="card mb-4">
            <div class="card-body">
                <?php
                // Parse categories (comma-separated list)
                $generatedCategories = array_map('trim', explode(',', $processedPost['processed_categories']));
                ?>
                
                <div class="<?php echo $isRtl ? 'text-end' : ''; ?>" dir="<?php echo $isRtl ? 'rtl' : 'ltr'; ?>">
                    <?php foreach ($generatedCategories as $category): ?>
                    <span class="badge bg-primary me-1 mb-1"><?php echo htmlspecialchars($category); ?></span>
                    <?php endforeach; ?>
                </div>
                
                <div class="mt-3">
                    <p class="text-muted mb-0">Raw response:</p>
                    <pre class="bg-light p-2 mt-2 rounded"><?php echo htmlspecialchars($processedPost['processed_categories']); ?></pre>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            Categories have been generated successfully.
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            No generated categories available. Use the form on the left to generate categories.
        </div>
        <?php endif; ?>
    </div>
</div>
