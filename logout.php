<?php
/**
 * Logout Script
 * 
 * This file handles user logout.
 */

// Load configuration
require_once 'config.php';

// Start session
session_start();

// Clear session variables
$_SESSION = [];

// Clear session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy session
session_destroy();

// Clear remember me cookie
setcookie('remember_token', '', time() - 3600, '/');

// Redirect to login page
header('Location: ' . BASE_URL . '/login.php');
exit;
