<div class="row">
    <div class="col-md-6">
        <h4 class="mb-3">Original Title</h4>
        <div class="card mb-4">
            <div class="card-body">
                <h5><?php echo htmlspecialchars($post['title']); ?></h5>
            </div>
        </div>
        
        <h4 class="mb-3">Process Title</h4>
        <div class="card mb-4">
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="process_title">
                    
                    <div class="mb-3">
                        <label for="task_type" class="form-label">Task Type</label>
                        <select class="form-select" id="task_type" name="task_type">
                            <option value="translate">Translate</option>
                            <option value="rewrite">Rewrite</option>
                            <option value="translate_rewrite">Translate & Rewrite</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-cog me-1"></i> Process Title
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <h4 class="mb-3">Processed Title</h4>
        
        <?php if ($processedPost && !empty($processedPost['processed_title'])): ?>
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="<?php echo $isRtl ? 'text-end' : ''; ?>" dir="<?php echo $isRtl ? 'rtl' : 'ltr'; ?>">
                    <?php echo htmlspecialchars($processedPost['processed_title']); ?>
                </h5>
            </div>
        </div>
        
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            Title has been processed successfully.
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            No processed title available. Use the form on the left to process the title.
        </div>
        <?php endif; ?>
    </div>
</div>
