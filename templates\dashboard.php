<?php
// Set page title
$pageTitle = 'Dashboard';

// Check if database update is needed
$needsUpdate = false;
try {
    // Try to access a column that should exist after the update
    $db->query("SELECT role FROM users LIMIT 1");
} catch (Exception $e) {
    $needsUpdate = true;
}

// Show update notification if needed
if ($needsUpdate) {
    echo '<div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Database Update Required:</strong> Your database needs to be updated to use all the new features.
        <a href="' . BASE_URL . '/update.php" class="btn btn-sm btn-primary ms-2">Update Now</a>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

// Get statistics
$totalJobs = $db->getValue("SELECT COUNT(*) FROM jobs");
$totalPosts = $db->getValue("SELECT COUNT(*) FROM posts");
$totalImages = $db->getValue("SELECT COUNT(*) FROM images");
$recentJobs = $db->query("SELECT * FROM jobs ORDER BY created_at DESC LIMIT 5");
$recentPosts = $db->query("SELECT p.*, j.name as job_name FROM posts p JOIN jobs j ON p.job_id = j.id ORDER BY p.created_at DESC LIMIT 6");
?>

<!-- Hero Welcome Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card card-gradient border-0 shadow-lg overflow-hidden position-relative">
            <!-- Decorative Background Elements -->
            <div class="position-absolute top-0 end-0 opacity-10">
                <i class="fas fa-download" style="font-size: 8rem; transform: rotate(15deg);"></i>
            </div>
            <div class="position-absolute bottom-0 start-0 opacity-5">
                <i class="fas fa-globe" style="font-size: 6rem; transform: rotate(-15deg);"></i>
            </div>

            <div class="card-body py-5 position-relative">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div class="mb-4">
                            <h1 class="display-5 fw-bold mb-3">
                                <i class="fas fa-rocket me-3"></i>
                                Welcome to AiCG
                            </h1>
                            <p class="lead mb-0 opacity-90">
                                Your intelligent content acquisition platform. Seamlessly extract, process, and manage content from WordPress sites and sitemaps with advanced AI capabilities.
                            </p>
                        </div>
                        <div class="d-flex flex-wrap gap-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-white bg-opacity-20 rounded-circle p-2 me-2">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                                <span class="small opacity-90">AI-Powered Processing</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-white bg-opacity-20 rounded-circle p-2 me-2">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                                <span class="small opacity-90">Automated Workflows</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-white bg-opacity-20 rounded-circle p-2 me-2">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                                <span class="small opacity-90">Smart Content Management</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 text-lg-end mt-4 mt-lg-0">
                        <div class="d-grid gap-2 d-lg-block">
                            <a href="<?php echo BASE_URL; ?>/?page=jobs&action=new" class="btn btn-light btn-lg px-4 py-3 shadow">
                                <i class="fas fa-plus me-2"></i>
                                Create New Job
                            </a>
                            <a href="<?php echo BASE_URL; ?>/?page=ai_settings" class="btn btn-outline-light btn-lg px-4 py-3 mt-2 mt-lg-0 ms-lg-2">
                                <i class="fas fa-brain me-2"></i>
                                AI Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Statistics Grid -->
<div class="row g-4 mb-5">
    <div class="col-xl-3 col-md-6">
        <div class="card card-elevated border-0 h-100 fade-in hover-lift">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-tasks text-primary" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small fw-semibold text-uppercase tracking-wide">Total Jobs</div>
                        <div class="h2 fw-bold text-primary mb-0"><?php echo number_format($totalJobs); ?></div>
                        <div class="text-muted small">Active content grabbing jobs</div>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo min(100, ($totalJobs / 10) * 100); ?>%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card card-elevated border-0 h-100 fade-in hover-lift" style="animation-delay: 0.1s;">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-newspaper text-success" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small fw-semibold text-uppercase tracking-wide">Total Posts</div>
                        <div class="h2 fw-bold text-success mb-0"><?php echo number_format($totalPosts); ?></div>
                        <div class="text-muted small">Content pieces grabbed</div>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo min(100, ($totalPosts / 100) * 100); ?>%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card card-elevated border-0 h-100 fade-in hover-lift" style="animation-delay: 0.2s;">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-images text-info" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small fw-semibold text-uppercase tracking-wide">Total Images</div>
                        <div class="h2 fw-bold text-info mb-0"><?php echo number_format($totalImages); ?></div>
                        <div class="text-muted small">Images downloaded</div>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo min(100, ($totalImages / 500) * 100); ?>%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card card-elevated border-0 h-100 fade-in hover-lift" style="animation-delay: 0.3s;">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-clock text-warning" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small fw-semibold text-uppercase tracking-wide">Last Activity</div>
                        <div class="h5 fw-bold text-warning mb-0">
                            <?php
                            $lastJob = $db->getRow("SELECT * FROM jobs WHERE last_run IS NOT NULL ORDER BY last_run DESC LIMIT 1");
                            echo $lastJob ? date('M d, H:i', strtotime($lastJob['last_run'])) : 'Never';
                            ?>
                        </div>
                        <div class="text-muted small">Most recent grab</div>
                    </div>
                </div>
                <div class="mt-3">
                    <?php if ($lastJob): ?>
                        <div class="d-flex align-items-center text-success small">
                            <i class="fas fa-circle me-1" style="font-size: 0.5rem;"></i>
                            System Active
                        </div>
                    <?php else: ?>
                        <div class="d-flex align-items-center text-muted small">
                            <i class="fas fa-circle me-1" style="font-size: 0.5rem;"></i>
                            No Activity
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Jobs</h5>
                <a href="<?php echo BASE_URL; ?>/?page=jobs" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> New Job
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (empty($recentJobs)): ?>
                <div class="p-4 text-center">
                    <p class="text-muted mb-0">No jobs found. Create your first job to start grabbing content.</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Last Run</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentJobs as $job): ?>
                            <tr>
                                <td>
                                    <a href="<?php echo BASE_URL; ?>/?page=jobs&action=view&id=<?php echo $job['id']; ?>">
                                        <?php echo htmlspecialchars($job['name']); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php if ($job['type'] === 'wordpress'): ?>
                                    <span class="badge bg-primary">WordPress</span>
                                    <?php else: ?>
                                    <span class="badge bg-info">Sitemap</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    switch ($job['status']) {
                                        case 'pending':
                                            echo '<span class="badge bg-secondary">Pending</span>';
                                            break;
                                        case 'running':
                                            echo '<span class="badge bg-warning">Running</span>';
                                            break;
                                        case 'completed':
                                            echo '<span class="badge bg-success">Completed</span>';
                                            break;
                                        case 'failed':
                                            echo '<span class="badge bg-danger">Failed</span>';
                                            break;
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php echo $job['last_run'] ? date('M d, H:i', strtotime($job['last_run'])) : 'Never'; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Posts</h5>
                <a href="<?php echo BASE_URL; ?>/?page=posts" class="btn btn-sm btn-primary">
                    <i class="fas fa-list me-1"></i> All Posts
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($recentPosts)): ?>
                <div class="text-center">
                    <p class="text-muted mb-0">No posts found. Run a job to grab content.</p>
                </div>
                <?php else: ?>
                <div class="row">
                    <?php foreach ($recentPosts as $post): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <?php
                            $featuredImage = json_decode($post['featured_image'], true);
                            $imagePath = $featuredImage ? str_replace(BASE_PATH, BASE_URL, $featuredImage['local_path']) : BASE_URL . '/assets/images/placeholder.jpg';
                            ?>
                            <img src="<?php echo $imagePath; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($post['title']); ?>" style="height: 150px; object-fit: cover;">
                            <div class="card-body">
                                <h6 class="card-title"><?php echo htmlspecialchars($post['title']); ?></h6>
                                <p class="card-text small text-muted">
                                    <i class="fas fa-calendar-alt me-1"></i> <?php echo date('M d, Y', strtotime($post['date_published'])); ?>
                                    <br>
                                    <i class="fas fa-tasks me-1"></i> <?php echo htmlspecialchars($post['job_name']); ?>
                                </p>
                                <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i> View
                                </a>
                                <a href="<?php echo BASE_URL; ?>/?page=process&id=<?php echo $post['id']; ?>" class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-cogs me-1"></i> Process
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card card-elevated border-0">
            <div class="card-header bg-gradient-to-r from-gray-50 to-white border-0 pb-0">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h4 class="mb-1 fw-bold">
                            <i class="fas fa-bolt text-primary me-2"></i>
                            Quick Actions
                        </h4>
                        <p class="text-muted small mb-0">Get started with these common tasks</p>
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-rocket fa-2x opacity-25"></i>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="row g-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="card border-2 border-primary border-opacity-25 h-100 hover-lift transition-all">
                            <div class="card-body text-center p-4">
                                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                                    <i class="fas fa-plus-circle text-primary fa-2x"></i>
                                </div>
                                <h5 class="fw-bold mb-2">Create New Job</h5>
                                <p class="text-muted small mb-3">Set up automated content grabbing from WordPress sites or sitemaps</p>
                                <a href="<?php echo BASE_URL; ?>/?page=jobs&action=new" class="btn btn-primary btn-sm px-4">
                                    Get Started
                                    <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6">
                        <div class="card border-2 border-success border-opacity-25 h-100 hover-lift transition-all">
                            <div class="card-body text-center p-4">
                                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                                    <i class="fas fa-play-circle text-success fa-2x"></i>
                                </div>
                                <h5 class="fw-bold mb-2">Manage Jobs</h5>
                                <p class="text-muted small mb-3">Run, monitor, and configure your existing content grabbing jobs</p>
                                <a href="<?php echo BASE_URL; ?>/?page=jobs" class="btn btn-success btn-sm px-4">
                                    View Jobs
                                    <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6">
                        <div class="card border-2 border-info border-opacity-25 h-100 hover-lift transition-all">
                            <div class="card-body text-center p-4">
                                <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                                    <i class="fas fa-newspaper text-info fa-2x"></i>
                                </div>
                                <h5 class="fw-bold mb-2">Browse Posts</h5>
                                <p class="text-muted small mb-3">View, search, and manage all your grabbed content and posts</p>
                                <a href="<?php echo BASE_URL; ?>/?page=posts" class="btn btn-info btn-sm px-4">
                                    View Posts
                                    <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6">
                        <div class="card border-2 border-warning border-opacity-25 h-100 hover-lift transition-all">
                            <div class="card-body text-center p-4">
                                <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                                    <i class="fas fa-brain text-warning fa-2x"></i>
                                </div>
                                <h5 class="fw-bold mb-2">AI Settings</h5>
                                <p class="text-muted small mb-3">Configure AI processing, workflows, and automation settings</p>
                                <a href="<?php echo BASE_URL; ?>/?page=ai_settings" class="btn btn-warning btn-sm px-4">
                                    Configure AI
                                    <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Quick Links -->
                <div class="row mt-4 pt-4 border-top">
                    <div class="col-12">
                        <div class="d-flex flex-wrap gap-3 justify-content-center">
                            <a href="<?php echo BASE_URL; ?>/?page=analytics" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-chart-bar me-1"></i> Analytics
                            </a>
                            <a href="<?php echo BASE_URL; ?>/?page=settings" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-cog me-1"></i> Settings
                            </a>
                            <a href="<?php echo BASE_URL; ?>/update_ai_tables.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-database me-1"></i> Update Database
                            </a>
                            <a href="<?php echo BASE_URL; ?>/setup/" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-tools me-1"></i> Setup Wizard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
