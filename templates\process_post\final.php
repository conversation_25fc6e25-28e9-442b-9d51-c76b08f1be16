<?php
// Check if we have all the necessary processed data
$isComplete = $processedPost &&
              !empty($processedPost['processed_title']) &&
              !empty($processedPost['processed_content']);

// Get SEO data if available
$seoData = [];
if ($processedPost && !empty($processedPost['processed_seo_metadata'])) {
    $seoText = $processedPost['processed_seo_metadata'];

    // Look for meta description
    if (preg_match('/meta\s+description:(.+?)(?:\n|$)/i', $seoText, $matches)) {
        $seoData['meta_description'] = trim($matches[1]);
    }

    // Look for keywords
    if (preg_match('/keywords:(.+?)(?:\n|$)/i', $seoText, $matches)) {
        $keywords = array_map('trim', explode(',', $matches[1]));
        $seoData['keywords'] = $keywords;
    }

    // Look for slug
    if (preg_match('/slug:(.+?)(?:\n|$)/i', $seoText, $matches)) {
        $seoData['slug'] = trim($matches[1]);
    }

    // Look for categories
    if (preg_match('/categories:(.+?)(?:\n|$)/i', $seoText, $matches)) {
        $categories = array_map('trim', explode(',', $matches[1]));
        $seoData['categories'] = $categories;
    }

    // Look for tags
    if (preg_match('/tags:(.+?)(?:\n|$)/i', $seoText, $matches)) {
        $tags = array_map('trim', explode(',', $matches[1]));
        $seoData['tags'] = $tags;
    }
}

// Get image URL if available
$imageUrl = '';
if ($processedPost && !empty($processedPost['processed_featured_image'])) {
    $imageUrl = str_replace(BASE_PATH, BASE_URL, $processedPost['processed_featured_image']);
}
?>

<?php if (!$isComplete): ?>
<div class="alert alert-warning">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>Incomplete Processing:</strong> You need to process at least the title and content before viewing the final result.
    <p class="mt-2 mb-0">Use the Title and Content tabs to process these elements.</p>
</div>
<?php else: ?>
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Processed Post Preview</h5>

                <div>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="printPost()">
                        <i class="fas fa-print me-1"></i> Print
                    </button>

                    <button type="button" class="btn btn-outline-success btn-sm ms-2" onclick="exportPost()">
                        <i class="fas fa-file-export me-1"></i> Export
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="post-preview" class="<?php echo $isRtl ? 'text-end' : ''; ?>" dir="<?php echo $isRtl ? 'rtl' : 'ltr'; ?>">
                    <h1 class="mb-4"><?php echo htmlspecialchars($processedPost['processed_title']); ?></h1>

                    <?php if (!empty($imageUrl)): ?>
                    <div class="text-center mb-4">
                        <img src="<?php echo htmlspecialchars($imageUrl); ?>" alt="Featured Image" class="img-fluid rounded">
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($seoData['meta_description'])): ?>
                    <div class="mb-4">
                        <p class="lead"><?php echo htmlspecialchars($seoData['meta_description']); ?></p>
                    </div>
                    <?php endif; ?>

                    <div class="post-content mb-4">
                        <?php echo $processedPost['processed_content']; ?>
                    </div>

                    <div class="post-meta mt-4 pt-3 border-top">
                        <?php
                        // Get processed categories
                        $processedCategories = [];
                        if (!empty($processedPost['processed_categories'])) {
                            $processedCategories = array_map('trim', explode(',', $processedPost['processed_categories']));
                        } elseif (!empty($seoData['categories'])) {
                            $processedCategories = $seoData['categories'];
                        }

                        // Get processed tags
                        $processedTags = [];
                        if (!empty($processedPost['processed_tags'])) {
                            $processedTags = array_map('trim', explode(',', $processedPost['processed_tags']));
                        } elseif (!empty($seoData['tags'])) {
                            $processedTags = $seoData['tags'];
                        }
                        ?>

                        <?php if (!empty($processedCategories)): ?>
                        <div class="mb-2">
                            <strong>Categories:</strong>
                            <?php foreach ($processedCategories as $category): ?>
                            <span class="badge bg-primary me-1"><?php echo htmlspecialchars($category); ?></span>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($processedTags)): ?>
                        <div>
                            <strong>Tags:</strong>
                            <?php foreach ($processedTags as $tag): ?>
                            <span class="badge bg-secondary me-1"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Next Steps:</strong> You can print or export this processed post, or continue processing other posts.
        </div>
    </div>
</div>

<script>
function printPost() {
    const printContents = document.getElementById('post-preview').innerHTML;
    const originalContents = document.body.innerHTML;

    document.body.innerHTML = `
        <div class="container py-4">
            ${printContents}
        </div>
    `;

    window.print();
    document.body.innerHTML = originalContents;

    // Reinitialize Bootstrap components
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

function exportPost() {
    const title = <?php echo json_encode($processedPost['processed_title'] ?? ''); ?>;
    const content = <?php echo json_encode($processedPost['processed_content'] ?? ''); ?>;
    const imageUrl = <?php echo json_encode($imageUrl); ?>;
    const metaDescription = <?php echo json_encode($seoData['meta_description'] ?? ''); ?>;

    // Get categories from processed categories or SEO data
    let categories = [];
    <?php if (!empty($processedPost['processed_categories'])): ?>
    categories = <?php echo json_encode(array_map('trim', explode(',', $processedPost['processed_categories']))); ?>;
    <?php elseif (!empty($seoData['categories'])): ?>
    categories = <?php echo json_encode($seoData['categories']); ?>;
    <?php endif; ?>

    // Get tags from processed tags or SEO data
    let tags = [];
    <?php if (!empty($processedPost['processed_tags'])): ?>
    tags = <?php echo json_encode(array_map('trim', explode(',', $processedPost['processed_tags']))); ?>;
    <?php elseif (!empty($seoData['tags'])): ?>
    tags = <?php echo json_encode($seoData['tags']); ?>;
    <?php endif; ?>

    const exportData = {
        title,
        content,
        featured_image: imageUrl,
        meta_description: metaDescription,
        categories,
        tags,
        language: <?php echo json_encode($language); ?>,
        is_rtl: <?php echo $isRtl ? 'true' : 'false'; ?>,
        original_post_id: <?php echo $postId; ?>,
        workflow_id: <?php echo $workflowId; ?>,
        processed_at: new Date().toISOString()
    };

    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(exportData, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `processed_post_${<?php echo $postId; ?>}_${new Date().toISOString().slice(0, 10)}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
}
</script>
<?php endif; ?>
