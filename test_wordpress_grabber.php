<?php
/**
 * Test WordPress Grabber
 * 
 * This script tests the WordPress grabber with a specific URL.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Check if the application is installed
if (!is_installed()) {
    echo "Application not installed. Please run the setup wizard first.\n";
    exit(1);
}

// Create content directories
create_content_directories();

// Load required files
require_once 'includes/Database.php';
require_once 'includes/ContentGrabber.php';
require_once 'includes/WordPressGrabber.php';

// Initialize database connection
$db = new Database();

// Get URL from query parameter
$url = isset($_GET['url']) ? $_GET['url'] : 'https://autogpt.net/';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 5;
$disable_embed = isset($_GET['disable_embed']) ? (bool)$_GET['disable_embed'] : false;

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress Grabber Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow: auto;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1>WordPress Grabber Test</h1>
        <p class="lead">Test the WordPress grabber with a specific URL</p>
        
        <form method="get" class="mb-4">
            <div class="mb-3">
                <label for="url" class="form-label">WordPress URL</label>
                <input type="url" class="form-control" id="url" name="url" value="' . htmlspecialchars($url) . '" required>
            </div>
            <div class="mb-3">
                <label for="limit" class="form-label">Number of posts to grab</label>
                <input type="number" class="form-control" id="limit" name="limit" value="' . $limit . '" min="1" max="100">
            </div>
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="disable_embed" name="disable_embed" value="1" ' . ($disable_embed ? 'checked' : '') . '>
                <label class="form-check-label" for="disable_embed">Disable _embed parameter (try this if no posts are found)</label>
            </div>
            <button type="submit" class="btn btn-primary">Test Grabber</button>
        </form>';

if (!empty($url)) {
    echo '<div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Test Results for: ' . htmlspecialchars($url) . '</h5>
            </div>
            <div class="card-body">
                <pre>';
    
    // Test URL accessibility
    echo "Testing URL accessibility...\n";
    $response = ContentGrabber::fetchUrl($url);
    if ($response) {
        echo "<span class='success'>URL is accessible! ✓</span>\n";
        echo "Response length: " . strlen($response) . " bytes\n\n";
        
        // Check if it's a WordPress site
        echo "Testing if it's a WordPress site...\n";
        $isWordPress = ContentGrabber::isWordPressSite($url);
        if ($isWordPress) {
            echo "<span class='success'>This is a WordPress site! ✓</span>\n\n";
            
            // Test WordPress API endpoints
            echo "Testing WordPress API endpoints...\n";
            $endpoints = [
                '/wp-json/' => 'Main API endpoint',
                '/wp-json/wp/v2/posts' => 'Posts endpoint',
                '/wp-json/wp/v2/posts?per_page=1' => 'Posts with limit',
                '/wp-json/wp/v2/posts?_embed=1&per_page=1' => 'Posts with _embed'
            ];
            
            foreach ($endpoints as $endpoint => $description) {
                $endpointUrl = rtrim($url, '/') . $endpoint;
                $endpointResponse = ContentGrabber::fetchUrl($endpointUrl);
                
                echo "- $description ($endpointUrl): ";
                if ($endpointResponse) {
                    $json = json_decode($endpointResponse, true);
                    if ($json !== null) {
                        echo "<span class='success'>Accessible ✓</span>\n";
                    } else {
                        echo "<span class='error'>Not valid JSON ✗</span>\n";
                    }
                } else {
                    echo "<span class='error'>Not accessible ✗</span>\n";
                }
            }
            
            // Create WordPress grabber
            echo "\nCreating WordPress grabber...\n";
            $options = [
                'disable_embed' => $disable_embed
            ];
            $grabber = new WordPressGrabber($db, $url, $options);
            
            // Grab posts
            echo "Grabbing $limit posts...\n";
            $startTime = microtime(true);
            $posts = $grabber->grab($limit);
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            
            if (empty($posts)) {
                echo "<span class='error'>No posts found! ✗</span>\n";
                echo "Try enabling the 'Disable _embed parameter' option above.\n";
            } else {
                echo "<span class='success'>Successfully grabbed " . count($posts) . " posts in $executionTime seconds! ✓</span>\n\n";
                
                // Display post details
                echo "Post details:\n";
                echo "------------\n";
                foreach ($posts as $index => $post) {
                    echo "Post #" . ($index + 1) . ":\n";
                    echo "  Title: " . $post['title'] . "\n";
                    echo "  Date: " . $post['date'] . "\n";
                    echo "  URL: " . $post['link'] . "\n";
                    echo "  Content length: " . strlen($post['content']) . " bytes\n";
                    echo "  Images: " . count($post['images']) . "\n";
                    echo "  Categories: " . count($post['categories']) . "\n";
                    echo "  Tags: " . count($post['tags']) . "\n";
                    echo "\n";
                }
            }
        } else {
            echo "<span class='error'>This is not a WordPress site. ✗</span>\n";
        }
    } else {
        echo "<span class='error'>URL is not accessible! ✗</span>\n";
        echo "Please check if the URL is correct and accessible from your server.\n";
    }
    
    echo '</pre>
            </div>
        </div>';
}

echo '    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
?>
