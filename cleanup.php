<?php
/**
 * Data Cleanup Tool
 *
 * This script provides functionality to clean up posts, images, and related data.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/Database.php';
require_once 'includes/JobManager.php';

// Initialize database connection
$db = new Database();

// Initialize JobManager
$jobManager = new JobManager($db);

// Get all jobs for dropdown
$jobs = $db->query("SELECT id, name FROM jobs ORDER BY name");

// Get post count
$postCount = $db->getValue("SELECT COUNT(*) FROM posts");

// Get image count
$imageCount = $db->getValue("SELECT COUNT(*) FROM images");

// Get orphaned image count
$orphanedImageCount = $db->getValue("SELECT COUNT(*) FROM images i LEFT JOIN posts p ON i.post_id = p.id WHERE p.id IS NULL");

// Process form submission
$result = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['cleanup_action'])) {
    $cleanupAction = $_POST['cleanup_action'];
    $jobId = isset($_POST['job_id']) ? (int)$_POST['job_id'] : 0;

    // Initialize result array
    $result = [
        'success' => true,
        'message' => '',
        'deleted_posts' => 0,
        'deleted_images' => 0,
        'deleted_files' => 0
    ];

    // Start transaction
    $db->beginTransaction();

    try {
        switch ($cleanupAction) {
            case 'job_data':
                // Clean up data for a specific job
                if ($jobId > 0) {
                    $job = $db->getRow("SELECT name FROM jobs WHERE id = ?", [$jobId]);
                    if ($job) {
                        // Get posts for this job
                        $posts = $db->query("SELECT id, slug, external_id, html_file, pdf_file FROM posts WHERE job_id = ?", [$jobId]);

                        if (!empty($posts)) {
                            foreach ($posts as $post) {
                                $postId = $post['id'];

                                // Delete images from filesystem
                                $images = $db->query("SELECT local_path FROM images WHERE post_id = ?", [$postId]);
                                foreach ($images as $image) {
                                    if (!empty($image['local_path']) && file_exists($image['local_path'])) {
                                        unlink($image['local_path']);
                                        $result['deleted_files']++;
                                    }
                                }

                                // Delete post directory if it exists
                                $postDir = IMAGES_DIR . '/' . $post['slug'] . '_' . $post['external_id'];
                                if (is_dir($postDir)) {
                                    removeDirectory($postDir);
                                }

                                // Delete HTML file if it exists
                                if (!empty($post['html_file']) && file_exists($post['html_file'])) {
                                    unlink($post['html_file']);
                                    $result['deleted_files']++;
                                }

                                // Delete PDF file if it exists
                                if (!empty($post['pdf_file']) && file_exists($post['pdf_file'])) {
                                    unlink($post['pdf_file']);
                                    $result['deleted_files']++;
                                }

                                // Delete post tags
                                $db->delete('post_tags', 'post_id = ?', [$postId]);

                                // Delete post categories
                                $db->delete('post_categories', 'post_id = ?', [$postId]);

                                // Delete post images from database
                                $imageCount = $db->getValue("SELECT COUNT(*) FROM images WHERE post_id = ?", [$postId]);
                                $db->delete('images', 'post_id = ?', [$postId]);
                                $result['deleted_images'] += $imageCount;
                            }

                            // Delete all posts for this job
                            $db->delete('posts', 'job_id = ?', [$jobId]);
                            $result['deleted_posts'] = count($posts);

                            $result['message'] = "Successfully cleaned up data for job '{$job['name']}'. Deleted {$result['deleted_posts']} posts, {$result['deleted_images']} images, and {$result['deleted_files']} files.";
                        } else {
                            $result['message'] = "No posts found for job '{$job['name']}'.";
                        }
                    } else {
                        $result['success'] = false;
                        $result['message'] = "Job not found.";
                    }
                } else {
                    $result['success'] = false;
                    $result['message'] = "Invalid job ID.";
                }
                break;

            case 'all_posts':
                // Clean up all posts and related data
                // Get all posts
                $posts = $db->query("SELECT id, slug, external_id, html_file, pdf_file FROM posts");

                if (!empty($posts)) {
                    foreach ($posts as $post) {
                        $postId = $post['id'];

                        // Delete images from filesystem
                        $images = $db->query("SELECT local_path FROM images WHERE post_id = ?", [$postId]);
                        foreach ($images as $image) {
                            if (!empty($image['local_path']) && file_exists($image['local_path'])) {
                                unlink($image['local_path']);
                                $result['deleted_files']++;
                            }
                        }

                        // Delete post directory if it exists
                        $postDir = IMAGES_DIR . '/' . $post['slug'] . '_' . $post['external_id'];
                        if (is_dir($postDir)) {
                            removeDirectory($postDir);
                        }

                        // Delete HTML file if it exists
                        if (!empty($post['html_file']) && file_exists($post['html_file'])) {
                            unlink($post['html_file']);
                            $result['deleted_files']++;
                        }

                        // Delete PDF file if it exists
                        if (!empty($post['pdf_file']) && file_exists($post['pdf_file'])) {
                            unlink($post['pdf_file']);
                            $result['deleted_files']++;
                        }

                        // Delete post tags
                        $db->delete('post_tags', 'post_id = ?', [$postId]);

                        // Delete post categories
                        $db->delete('post_categories', 'post_id = ?', [$postId]);

                        // Delete post images from database
                        $imageCount = $db->getValue("SELECT COUNT(*) FROM images WHERE post_id = ?", [$postId]);
                        $db->delete('images', 'post_id = ?', [$postId]);
                        $result['deleted_images'] += $imageCount;
                    }

                    // Delete all posts - using DELETE instead of TRUNCATE to avoid foreign key constraint issues
                    $db->query("DELETE FROM posts");
                    $result['deleted_posts'] = count($posts);

                    $result['message'] = "Successfully cleaned up all posts and related data. Deleted {$result['deleted_posts']} posts, {$result['deleted_images']} images, and {$result['deleted_files']} files.";
                } else {
                    $result['message'] = "No posts found.";
                }
                break;

            case 'orphaned_images':
                // Clean up orphaned images (images without a post)
                // Get all orphaned images
                $images = $db->query("SELECT i.id, i.local_path FROM images i LEFT JOIN posts p ON i.post_id = p.id WHERE p.id IS NULL");

                if (!empty($images)) {
                    foreach ($images as $image) {
                        // Delete image file if it exists
                        if (!empty($image['local_path']) && file_exists($image['local_path'])) {
                            unlink($image['local_path']);
                            $result['deleted_files']++;
                        }

                        // Delete image from database
                        $db->delete('images', 'id = ?', [$image['id']]);
                        $result['deleted_images']++;
                    }

                    $result['message'] = "Successfully cleaned up orphaned images. Deleted {$result['deleted_images']} images and {$result['deleted_files']} files.";
                } else {
                    $result['message'] = "No orphaned images found.";
                }
                break;

            default:
                $result['success'] = false;
                $result['message'] = "Invalid cleanup action.";
                break;
        }

        // Commit transaction
        $db->commit();
    } catch (Exception $e) {
        // Rollback transaction on error
        $db->rollback();

        $result = [
            'success' => false,
            'message' => "Error: " . $e->getMessage(),
            'deleted_posts' => 0,
            'deleted_images' => 0,
            'deleted_files' => 0
        ];
    }

    // Refresh counts after cleanup
    $postCount = $db->getValue("SELECT COUNT(*) FROM posts");
    $imageCount = $db->getValue("SELECT COUNT(*) FROM images");
    $orphanedImageCount = $db->getValue("SELECT COUNT(*) FROM images i LEFT JOIN posts p ON i.post_id = p.id WHERE p.id IS NULL");
}

/**
 * Recursively remove a directory and its contents
 *
 * @param string $dir Directory path
 * @return bool Success status
 */
function removeDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }

    $files = array_diff(scandir($dir), ['.', '..']);

    foreach ($files as $file) {
        $path = $dir . '/' . $file;

        if (is_dir($path)) {
            removeDirectory($path);
        } else {
            unlink($path);
        }
    }

    return rmdir($dir);
}

// Set page title
$pageTitle = 'Data Cleanup';

// Include tool header
include_once 'includes/tool_header.php';

// Display result message if available
if ($result): ?>
<div class="alert alert-<?php echo $result['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($result['message']); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

        <div class="row">
            <div class="col-md-4">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Current Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center mb-3">
                                <div class="display-4"><?php echo $postCount; ?></div>
                                <div class="text-muted">Posts</div>
                            </div>
                            <div class="col-md-4 text-center mb-3">
                                <div class="display-4"><?php echo $imageCount; ?></div>
                                <div class="text-muted">Images</div>
                            </div>
                            <div class="col-md-4 text-center mb-3">
                                <div class="display-4"><?php echo $orphanedImageCount; ?></div>
                                <div class="text-muted">Orphaned</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Cleanup Options</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" id="cleanupForm">
                            <div class="mb-3">
                                <label for="cleanup_action" class="form-label">Cleanup Action</label>
                                <select class="form-select" id="cleanup_action" name="cleanup_action" required>
                                    <option value="">Select an action</option>
                                    <option value="job_data">Clean up data for a specific job</option>
                                    <option value="all_posts">Clean up all posts and related data</option>
                                    <option value="orphaned_images">Clean up orphaned images</option>
                                </select>
                            </div>

                            <div class="mb-3" id="jobSelectContainer" style="display: none;">
                                <label for="job_id" class="form-label">Select Job</label>
                                <select class="form-select" id="job_id" name="job_id">
                                    <option value="">Select a job</option>
                                    <?php foreach ($jobs as $job): ?>
                                    <option value="<?php echo $job['id']; ?>"><?php echo htmlspecialchars($job['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="alert alert-warning" id="warningMessage" style="display: none;">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span id="warningText"></span>
                            </div>

                            <button type="submit" class="btn btn-danger" id="cleanupButton" disabled>
                                <i class="fas fa-trash me-1"></i> Perform Cleanup
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const cleanupActionSelect = document.getElementById('cleanup_action');
        const jobSelectContainer = document.getElementById('jobSelectContainer');
        const jobSelect = document.getElementById('job_id');
        const warningMessage = document.getElementById('warningMessage');
        const warningText = document.getElementById('warningText');
        const cleanupButton = document.getElementById('cleanupButton');
        const cleanupForm = document.getElementById('cleanupForm');

        cleanupActionSelect.addEventListener('change', function() {
            const action = this.value;

            // Reset form
            cleanupButton.disabled = true;
            warningMessage.style.display = 'none';
            jobSelectContainer.style.display = 'none';

            if (action === 'job_data') {
                // Show job select
                jobSelectContainer.style.display = 'block';
                warningText.textContent = 'This will delete all posts, images, and related data for the selected job. This action cannot be undone.';
                warningMessage.style.display = 'block';

                // Enable button only when a job is selected
                jobSelect.addEventListener('change', function() {
                    cleanupButton.disabled = !this.value;
                });
            } else if (action === 'all_posts') {
                warningText.textContent = 'This will delete ALL posts, images, and related data from the system. This action cannot be undone.';
                warningMessage.style.display = 'block';
                cleanupButton.disabled = false;
            } else if (action === 'orphaned_images') {
                warningText.textContent = 'This will delete all images that are not associated with any post. This action cannot be undone.';
                warningMessage.style.display = 'block';
                cleanupButton.disabled = false;
            }
        });

        cleanupForm.addEventListener('submit', function(e) {
            const action = cleanupActionSelect.value;

            if (action === 'job_data' && !jobSelect.value) {
                e.preventDefault();
                alert('Please select a job.');
                return;
            }

            if (!confirm('Are you sure you want to perform this cleanup action? This cannot be undone.')) {
                e.preventDefault();
            }
        });
    });
</script>

<?php
// Include tool footer
include_once 'includes/tool_footer.php';
?>
