<?php
/**
 * AutoGPT.net Content Grabber
 *
 * This class is specifically designed to grab content from autogpt.net.
 */
class AutoGptGrabber extends ContentGrabber {
    /**
     * Sanitize a string to be used as a slug
     *
     * @param string $title The string to sanitize
     * @return string Sanitized string
     */
    private function sanitize_title($title) {
        // Convert to lowercase
        $title = strtolower($title);

        // Remove special characters
        $title = preg_replace('/[^a-z0-9\s-]/', '', $title);

        // Replace spaces with hyphens
        $title = preg_replace('/\s+/', '-', $title);

        // Remove multiple hyphens
        $title = preg_replace('/-+/', '-', $title);

        // Trim hyphens from beginning and end
        $title = trim($title, '-');

        // If empty, use a default
        if (empty($title)) {
            $title = 'post';
        }

        return $title;
    }
    /**
     * Grab content from autogpt.net
     *
     * @param int $limit Maximum number of posts to grab
     * @return array Grabbed content
     */
    public function grab($limit = 10) {
        $posts = [];
        $page = 1;
        $perPage = min($limit, 100); // WordPress API usually limits to 100 per page
        $total = 0;

        // Debug mode
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("AutoGptGrabber: Starting grab with limit $limit");
        }

        // Build the API URL with filters
        $apiUrl = rtrim($this->url, '/') . '/wp-json/wp/v2/posts?per_page=' . $perPage;

        // Add orderby and order parameters to get the most recent posts first
        $apiUrl .= '&orderby=date&order=desc';

        // Add _embed parameter for featured images and terms (categories/tags)
        // Some WordPress sites might have issues with this parameter, so we'll make it optional
        if (!isset($this->options['disable_embed']) || !$this->options['disable_embed']) {
            $apiUrl .= '&_embed=1';
        }

        // Add category filter if specified
        if (!empty($this->options['category'])) {
            $apiUrl .= '&categories=' . $this->options['category'];
        }

        // Add date filter if specified
        if (!empty($this->options['after_date'])) {
            $apiUrl .= '&after=' . date('c', strtotime($this->options['after_date']));
        }

        // Add date range filter if specified
        if (!empty($this->options['before_date'])) {
            $apiUrl .= '&before=' . date('c', strtotime($this->options['before_date']));
        }

        // Log the API URL if in debug mode
        if ($debug) {
            error_log("AutoGptGrabber: API URL: $apiUrl");
        }

        // Try different approaches if the site is autogpt.net
        $isAutogpt = (strpos($this->url, 'autogpt.net') !== false);
        $approaches = [
            'default' => $apiUrl,
            'no_embed' => rtrim($this->url, '/') . '/wp-json/wp/v2/posts?per_page=' . $perPage . '&orderby=date&order=desc',
            'simple' => rtrim($this->url, '/') . '/wp-json/wp/v2/posts?per_page=' . $perPage,
            'minimal' => rtrim($this->url, '/') . '/wp-json/wp/v2/posts'
        ];

        if ($isAutogpt && $debug) {
            error_log("AutoGptGrabber: Using multiple approaches for autogpt.net");
        }

        foreach ($approaches as $approach => $baseUrl) {
            if ($debug) {
                error_log("AutoGptGrabber: Trying approach: $approach with URL: $baseUrl");
            }

            // Reset page counter for each approach
            $page = 1;

            // Fetch posts until we reach the limit
            while (count($posts) < $limit) {
                $pageUrl = $baseUrl . ($approach !== 'minimal' ? '&page=' . $page : '?page=' . $page);

                if ($debug) {
                    error_log("AutoGptGrabber: Fetching page $page: $pageUrl");
                }

                // Use direct cURL instead of fetchUrl
                $response = $this->directFetch($pageUrl);

                if (!$response) {
                    // Log the error
                    error_log("AutoGptGrabber: Failed to fetch URL: $pageUrl");
                    break;
                }

                $data = json_decode($response, true);

                if (empty($data) || !is_array($data)) {
                    // Log the error
                    error_log("AutoGptGrabber: Invalid JSON response from URL: $pageUrl");
                    error_log("Response: " . substr($response, 0, 500) . "...");
                    break;
                }

                // Check if the response is an error message
                if (isset($data['code']) && isset($data['message'])) {
                    error_log("AutoGptGrabber: WordPress API error: {$data['code']} - {$data['message']}");
                    break;
                }

                // If we got posts, process them
                if (count($data) > 0) {
                    if ($debug) {
                        error_log("AutoGptGrabber: Found " . count($data) . " posts with approach: $approach");
                    }

                    foreach ($data as $post) {
                        try {
                            $processedPost = $this->processPost($post);
                            if ($processedPost) {
                                $posts[] = $processedPost;
                                $total++;

                                if ($total >= $limit) {
                                    break 2; // Break out of both loops
                                }
                            }
                        } catch (Exception $e) {
                            error_log("AutoGptGrabber: Error processing post: " . $e->getMessage());
                            // Continue with the next post
                            continue;
                        }
                    }

                    // Check if we've reached the last page
                    if (count($data) < $perPage) {
                        break;
                    }

                    $page++;
                } else {
                    // No posts found with this approach
                    if ($debug) {
                        error_log("AutoGptGrabber: No posts found with approach: $approach");
                    }
                    break;
                }
            }

            // If we got posts, no need to try other approaches
            if (count($posts) > 0) {
                if ($debug) {
                    error_log("AutoGptGrabber: Successfully grabbed " . count($posts) . " posts with approach: $approach");
                }
                break;
            }
        }

        return $posts;
    }

    /**
     * Direct fetch using cURL
     *
     * @param string $url URL to fetch
     * @return string|bool Response or false on failure
     */
    protected function directFetch($url) {
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("AutoGptGrabber: Direct fetching URL: $url");
        }

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // Increase timeout to 60 seconds
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HEADER, false);

        // Set user agent
        $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);

        // Set headers
        $headers = [
            'Accept: application/json',
            'Cache-Control: no-cache',
            'Pragma: no-cache'
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

        if ($debug) {
            error_log("AutoGptGrabber: HTTP Code: $httpCode, Content-Type: $contentType, Response length: " . strlen($response));
        }

        // Check for errors
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            if ($debug) {
                error_log("AutoGptGrabber: cURL error: $error");
            }
            curl_close($ch);
            return false;
        }

        curl_close($ch);

        // Check if response is successful
        if ($httpCode < 200 || $httpCode >= 300) {
            if ($debug) {
                error_log("AutoGptGrabber: HTTP error: $httpCode");
            }
            return false;
        }

        // Validate JSON response
        if (strpos($contentType, 'application/json') !== false) {
            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                if ($debug) {
                    error_log("AutoGptGrabber: Invalid JSON response: " . json_last_error_msg());
                    error_log("AutoGptGrabber: Response: " . substr($response, 0, 500) . "...");
                }

                // Try to clean up the response
                $response = preg_replace('/[\x00-\x1F\x7F]/', '', $response);
                $data = json_decode($response, true);
                if (json_last_error() !== JSON_ERROR_NONE && $debug) {
                    error_log("AutoGptGrabber: Still invalid JSON after cleanup: " . json_last_error_msg());
                    return false;
                }
            }
        }

        return $response;
    }

    /**
     * Process a WordPress post
     *
     * @param array $post WordPress post data
     * @return array Processed post data
     */
    protected function processPost($post) {
        // Debug mode
        $debug = isset($this->options['debug']) && $this->options['debug'];

        // Validate post data
        if (!isset($post['id']) || !isset($post['title']) || !isset($post['content'])) {
            if ($debug) {
                error_log("AutoGptGrabber: Invalid post data - missing required fields");
                error_log("AutoGptGrabber: Post data: " . json_encode($post));
            }
            return false;
        }

        // Create a unique directory for this post
        $postId = $post['id'];
        $postSlug = isset($post['slug']) ? $post['slug'] : sanitize_title($post['title']['rendered']);
        $postDir = IMAGES_DIR . '/' . $postSlug . '_' . $postId;

        // Check if the post date is in the future
        $postDate = isset($post['date']) ? strtotime($post['date']) : time();
        $isFuture = $postDate > time();

        if ($debug) {
            error_log("AutoGptGrabber: Processing post {$postId} with date " .
                (isset($post['date']) ? $post['date'] : 'unknown') .
                " (future: " . ($isFuture ? 'yes' : 'no') . ")");
        }

        // Extract post data with fallbacks for missing fields
        $processedPost = [
            'id' => $postId,
            'title' => isset($post['title']['rendered']) ? html_entity_decode($post['title']['rendered']) : 'Untitled',
            'content' => isset($post['content']['rendered']) ? $post['content']['rendered'] : '',
            'excerpt' => isset($post['excerpt']['rendered']) ? $post['excerpt']['rendered'] : '',
            'date' => isset($post['date']) ? $post['date'] : date('Y-m-d H:i:s'),
            'modified' => isset($post['modified']) ? $post['modified'] : date('Y-m-d H:i:s'),
            'slug' => $postSlug,
            'link' => isset($post['link']) ? $post['link'] : '',
            'is_future' => $isFuture,
            'categories' => [],
            'tags' => [],
            'images' => [],
            'featured_image' => null
        ];

        // Extract categories
        if (isset($post['_embedded']['wp:term'])) {
            foreach ($post['_embedded']['wp:term'] as $terms) {
                foreach ($terms as $term) {
                    if ($term['taxonomy'] === 'category') {
                        $processedPost['categories'][] = [
                            'id' => $term['id'],
                            'name' => $term['name'],
                            'slug' => $term['slug']
                        ];
                    } elseif ($term['taxonomy'] === 'post_tag') {
                        $processedPost['tags'][] = [
                            'id' => $term['id'],
                            'name' => $term['name'],
                            'slug' => $term['slug']
                        ];
                    }
                }
            }
        }

        // Extract featured image
        if (isset($post['_embedded']['wp:featuredmedia'][0])) {
            $featuredMedia = $post['_embedded']['wp:featuredmedia'][0];
            if (isset($featuredMedia['source_url'])) {
                $localPath = $this->downloadImage($featuredMedia['source_url'], $postDir);
                if ($localPath) {
                    $processedPost['featured_image'] = [
                        'url' => $featuredMedia['source_url'],
                        'local_path' => $localPath,
                        'alt' => isset($featuredMedia['alt_text']) ? $featuredMedia['alt_text'] : '',
                        'caption' => isset($featuredMedia['caption']['rendered']) ? $featuredMedia['caption']['rendered'] : ''
                    ];
                }
            }
        }

        // Generate HTML and PDF versions
        $this->generateHtmlVersion($processedPost);
        $this->generatePdfVersion($processedPost);

        return $processedPost;
    }

    /**
     * Generate HTML version of the post
     *
     * @param array $post Processed post data
     * @return string Path to the HTML file
     */
    protected function generateHtmlVersion($post) {
        // Create the HTML directory if it doesn't exist
        if (!file_exists(HTML_DIR)) {
            mkdir(HTML_DIR, 0755, true);
        }

        // Create HTML file
        $htmlFile = HTML_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.html';

        // Generate HTML content
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($post['title']) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>' . htmlspecialchars($post['title']) . '</h1>
    <div class="meta">Published on: ' . date('F j, Y', strtotime($post['date'])) . '</div>
    <div class="content">' . $post['content'] . '</div>
</body>
</html>';

        // Save HTML file
        file_put_contents($htmlFile, $html);

        return $htmlFile;
    }

    /**
     * Generate PDF version of the post
     *
     * @param array $post Processed post data
     * @return string Path to the PDF file
     */
    protected function generatePdfVersion($post) {
        // Create the PDF directory if it doesn't exist
        if (!file_exists(PDF_DIR)) {
            mkdir(PDF_DIR, 0755, true);
        }

        // PDF file path
        $pdfFile = PDF_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.pdf';

        // Simple PDF generation
        $pdfContent = "PDF version of: " . $post['title'];
        file_put_contents($pdfFile, $pdfContent);

        return $pdfFile;
    }
}
?>
