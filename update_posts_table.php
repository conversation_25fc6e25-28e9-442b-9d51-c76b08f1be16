<?php
/**
 * Update Posts Table
 * 
 * This script adds the is_future column to the posts table.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Check if the application is installed
if (!is_installed()) {
    echo "Application not installed. Please run the setup wizard first.\n";
    exit(1);
}

// Load required files
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Posts Table</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container py-5">
        <h1>Update Posts Table</h1>';

// Check if the is_future column already exists
$columnExists = false;
try {
    $db->query("SELECT is_future FROM posts LIMIT 1");
    $columnExists = true;
    
    echo '<div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        The is_future column already exists in the posts table. No update needed.
    </div>';
} catch (Exception $e) {
    // Column doesn't exist, we'll add it
    echo '<div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        The is_future column does not exist in the posts table. This update will add it.
    </div>';
}

// If the column doesn't exist, add it
if (!$columnExists) {
    if (isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
        try {
            // Start transaction
            $db->beginTransaction();
            
            // Add is_future column to posts table
            $db->query("ALTER TABLE posts ADD COLUMN is_future TINYINT(1) NOT NULL DEFAULT 0");
            
            // Update existing posts to set is_future based on date_published
            $db->query("UPDATE posts SET is_future = IF(date_published > NOW(), 1, 0)");
            
            // Commit transaction
            $db->commit();
            
            echo '<div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                The is_future column has been added to the posts table successfully!
            </div>';
        } catch (Exception $e) {
            // Rollback transaction
            $db->rollback();
            
            echo '<div class="alert alert-danger">
                <i class="fas fa-times-circle me-2"></i>
                Error updating posts table: ' . htmlspecialchars($e->getMessage()) . '
            </div>';
        }
    } else {
        // Show confirmation form
        echo '<div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Confirm Update</h5>
            </div>
            <div class="card-body">
                <p>This update will add a new column to the posts table:</p>
                <ul>
                    <li><strong>is_future</strong> - Indicates if a post has a future publication date</li>
                </ul>
                <p>This will enable better handling of future-dated posts.</p>
                
                <form method="post">
                    <input type="hidden" name="confirm" value="yes">
                    <button type="submit" class="btn btn-primary">Run Update</button>
                    <a href="' . BASE_URL . '/" class="btn btn-secondary ms-2">Cancel</a>
                </form>
            </div>
        </div>';
    }
}

// Add links to other tools
echo '<div class="mt-4">
    <h4>Other Tools</h4>
    <div class="list-group">
        <a href="debug_job.php" class="list-group-item list-group-item-action">
            <i class="fas fa-bug me-2"></i> Job Debugger
        </a>
        <a href="test_url.php" class="list-group-item list-group-item-action">
            <i class="fas fa-link me-2"></i> URL Test Tool
        </a>
        <a href="test_wordpress_grabber.php" class="list-group-item list-group-item-action">
            <i class="fas fa-wordpress me-2"></i> WordPress Grabber Test
        </a>
        <a href="' . BASE_URL . '/?page=jobs" class="list-group-item list-group-item-action">
            <i class="fas fa-tasks me-2"></i> Back to Jobs
        </a>
    </div>
</div>';

echo '</div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</body>
</html>';
?>
