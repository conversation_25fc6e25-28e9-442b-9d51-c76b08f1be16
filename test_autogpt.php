<?php
/**
 * Test AutoGPT.net Grabber
 * 
 * This script specifically tests grabbing content from autogpt.net.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Create content directories
create_content_directories();

// Load required files
require_once 'includes/Database.php';
require_once 'includes/ContentGrabber.php';
require_once 'includes/WordPressGrabber.php';

// Initialize database connection
$db = new Database();

// URL to test
$url = 'https://autogpt.net';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 5;
$disable_embed = isset($_GET['disable_embed']) ? (bool)$_GET['disable_embed'] : false;
$debug = true;

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AutoGPT.net Grabber</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow: auto;
            max-height: 400px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .warning {
            color: orange;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1>Test AutoGPT.net Grabber</h1>
        <p class="lead">This tool specifically tests grabbing content from autogpt.net.</p>
        
        <form method="get" class="mb-4">
            <div class="mb-3">
                <label for="limit" class="form-label">Number of posts to grab</label>
                <input type="number" class="form-control" id="limit" name="limit" value="' . $limit . '" min="1" max="100">
            </div>
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="disable_embed" name="disable_embed" value="1" ' . ($disable_embed ? 'checked' : '') . '>
                <label class="form-check-label" for="disable_embed">Disable _embed parameter</label>
            </div>
            <button type="submit" class="btn btn-primary">Test Grabber</button>
        </form>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Test Results</h5>
            </div>
            <div class="card-body">
                <pre>';

echo "Testing AutoGPT.net Grabber\n";
echo "URL: $url\n";
echo "Limit: $limit posts\n";
echo "Disable _embed: " . ($disable_embed ? 'Yes' : 'No') . "\n";
echo "Debug mode: Enabled\n\n";

// Create a custom error handler to capture all errors
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    echo "PHP ERROR [$errno]: $errstr in $errfile on line $errline\n";
    return true;
});

try {
    // Create grabber with debug mode
    echo "Creating WordPress grabber...\n";
    $options = [
        'debug' => $debug,
        'disable_embed' => $disable_embed
    ];
    
    // Test direct API access
    echo "Testing direct API access...\n";
    $apiUrl = rtrim($url, '/') . '/wp-json/wp/v2/posts?per_page=1&orderby=date&order=desc';
    if ($disable_embed) {
        $apiUrl .= '&_embed=0';
    } else {
        $apiUrl .= '&_embed=1';
    }
    
    echo "API URL: $apiUrl\n";
    $response = ContentGrabber::fetchUrl($apiUrl, ['debug' => $debug]);
    
    if ($response) {
        echo "API access successful! ✓\n";
        echo "Response length: " . strlen($response) . " bytes\n";
        
        // Parse response
        $data = json_decode($response, true);
        if (is_array($data) && !empty($data)) {
            echo "Found " . count($data) . " posts! ✓\n";
            
            // Display post details
            $post = $data[0];
            echo "Post details:\n";
            echo "  ID: " . $post['id'] . "\n";
            echo "  Title: " . $post['title']['rendered'] . "\n";
            echo "  Date: " . $post['date'] . "\n";
            echo "  Status: " . $post['status'] . "\n";
            echo "  Link: " . $post['link'] . "\n\n";
            
            // Check if the post date is in the future
            $postDate = strtotime($post['date']);
            $now = time();
            if ($postDate > $now) {
                echo "This post has a future date! (" . date('Y-m-d H:i:s', $postDate) . ")\n";
                echo "Current time: " . date('Y-m-d H:i:s', $now) . "\n";
                echo "Time difference: " . ($postDate - $now) . " seconds (" . round(($postDate - $now) / 86400) . " days)\n\n";
            }
        } else {
            echo "Failed to parse JSON response! ✗\n";
            echo "Response: " . substr($response, 0, 500) . "...\n\n";
        }
    } else {
        echo "API access failed! ✗\n\n";
    }
    
    // Create grabber
    $grabber = new WordPressGrabber($db, $url, $options);
    
    // Grab posts
    echo "Grabbing $limit posts...\n";
    $startTime = microtime(true);
    $posts = $grabber->grab($limit);
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);
    
    if (empty($posts)) {
        echo "No posts grabbed! ✗\n";
        echo "Execution time: $executionTime seconds\n\n";
    } else {
        echo "Successfully grabbed " . count($posts) . " posts in $executionTime seconds! ✓\n\n";
        
        // Display post details
        echo "Post details:\n";
        echo "------------\n";
        foreach ($posts as $index => $post) {
            echo "Post #" . ($index + 1) . ":\n";
            echo "  ID: " . $post['id'] . "\n";
            echo "  Title: " . $post['title'] . "\n";
            echo "  Date: " . $post['date'] . "\n";
            echo "  Is future: " . ($post['is_future'] ? 'Yes' : 'No') . "\n";
            echo "  Link: " . $post['link'] . "\n";
            echo "  Content length: " . strlen($post['content']) . " bytes\n";
            echo "  Images: " . count($post['images']) . "\n";
            echo "  Categories: " . count($post['categories']) . "\n";
            echo "  Tags: " . count($post['tags']) . "\n";
            echo "\n";
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . " ✗\n";
    echo "File: " . $e->getFile() . " on line " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Restore error handler
restore_error_handler();

echo '</pre>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="web_run_job.php" class="btn btn-secondary">Back to Web Job Runner</a>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
?>
