<?php
/**
 * Configuration file for Content Grabber
 *
 * This file contains all the configuration settings for the application.
 */

// Define base path for dynamic installation
define('BASE_PATH', dirname(__FILE__));
define('BASE_URL', (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]" . dirname($_SERVER['PHP_SELF']));

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', '');
define('DB_USER', '');
define('DB_PASS', '');

// Application settings
define('APP_NAME', 'Content Grabber');
define('APP_VERSION', '1.0.0');

// Content settings
define('POSTS_PER_PAGE', 10);
define('MAX_POSTS_TO_GRAB', 100);
define('IMAGES_DIR', BASE_PATH . '/content/images');
define('HTML_DIR', BASE_PATH . '/content/html');
define('PDF_DIR', BASE_PATH . '/content/pdf');
define('AI_IMAGES_DIR', BASE_PATH . '/content/images/ai_generated');

// Check if the application is installed
function is_installed() {
    return (DB_HOST != '' && DB_NAME != '' && DB_USER != '');
}

// Get database connection
function get_db_connection() {
    static $conn;

    if (!$conn) {
        try {
            $conn = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]
            );
        } catch (PDOException $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }

    return $conn;
}

// Create content directories if they don't exist
function create_content_directories() {
    $dirs = [
        BASE_PATH . '/content',
        IMAGES_DIR,
        HTML_DIR,
        PDF_DIR,
        AI_IMAGES_DIR
    ];

    foreach ($dirs as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}

// Load environment variables if .env file exists
if (file_exists(BASE_PATH . '/.env')) {
    $env = parse_ini_file(BASE_PATH . '/.env');
    foreach ($env as $key => $value) {
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}
