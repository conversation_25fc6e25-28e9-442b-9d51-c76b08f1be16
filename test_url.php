<?php
/**
 * URL Testing Script
 * 
 * This script helps test if a URL is a WordPress site or sitemap.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/ContentGrabber.php';

// Get URL from query parameter
$url = isset($_GET['url']) ? $_GET['url'] : '';

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Test Tool</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow: auto;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1>URL Test Tool</h1>
        <p class="lead">Test if a URL is a WordPress site or sitemap</p>
        
        <form method="get" class="mb-4">
            <div class="input-group mb-3">
                <input type="url" class="form-control" name="url" placeholder="Enter URL to test" value="' . htmlspecialchars($url) . '" required>
                <button class="btn btn-primary" type="submit">Test URL</button>
            </div>
        </form>';

if (!empty($url)) {
    echo '<div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Test Results for: ' . htmlspecialchars($url) . '</h5>
            </div>
            <div class="card-body">
                <pre>';
    
    // Test URL accessibility
    echo "Testing URL accessibility...\n";
    $response = ContentGrabber::fetchUrl($url);
    if ($response) {
        echo "<span class='success'>URL is accessible! ✓</span>\n";
        echo "Response length: " . strlen($response) . " bytes\n\n";
        
        // Check if it's a WordPress site
        echo "Testing if it's a WordPress site...\n";
        $isWordPress = ContentGrabber::isWordPressSite($url);
        if ($isWordPress) {
            echo "<span class='success'>This is a WordPress site! ✓</span>\n";
            echo "You should use type='wordpress' when creating a job for this URL.\n\n";
            
            // Test WordPress API endpoints
            echo "Testing WordPress API endpoints...\n";
            $endpoints = [
                '/wp-json/' => 'Main API endpoint',
                '/wp-json/wp/v2/posts' => 'Posts endpoint',
                '/wp-json/wp/v2/categories' => 'Categories endpoint',
                '/wp-json/wp/v2/tags' => 'Tags endpoint',
                '/wp-json/wp/v2/media' => 'Media endpoint'
            ];
            
            foreach ($endpoints as $endpoint => $description) {
                $endpointUrl = rtrim($url, '/') . $endpoint;
                $endpointResponse = ContentGrabber::fetchUrl($endpointUrl);
                
                echo "- $description ($endpointUrl): ";
                if ($endpointResponse) {
                    $json = json_decode($endpointResponse);
                    if ($json !== null) {
                        echo "<span class='success'>Accessible ✓</span>\n";
                    } else {
                        echo "<span class='error'>Not valid JSON ✗</span>\n";
                    }
                } else {
                    echo "<span class='error'>Not accessible ✗</span>\n";
                }
            }
        } else {
            echo "<span class='error'>This is not a WordPress site. ✗</span>\n\n";
        }
        
        // Check if it's a sitemap
        echo "\nTesting if it's a sitemap...\n";
        $isSitemap = ContentGrabber::isSitemap($url);
        if ($isSitemap) {
            echo "<span class='success'>This is a sitemap! ✓</span>\n";
            echo "You should use type='sitemap' when creating a job for this URL.\n\n";
            
            // Parse sitemap to get URL count
            $xml = simplexml_load_string($response);
            if ($xml) {
                if ($xml->getName() == 'sitemapindex') {
                    $sitemaps = $xml->sitemap;
                    echo "This is a sitemap index with " . count($sitemaps) . " sitemaps.\n";
                    
                    // Show first few sitemaps
                    echo "First few sitemaps:\n";
                    $count = 0;
                    foreach ($sitemaps as $sitemap) {
                        echo "- " . $sitemap->loc . "\n";
                        $count++;
                        if ($count >= 5) {
                            echo "... and " . (count($sitemaps) - 5) . " more\n";
                            break;
                        }
                    }
                } elseif ($xml->getName() == 'urlset') {
                    $urls = $xml->url;
                    echo "This is a sitemap with " . count($urls) . " URLs.\n";
                    
                    // Show first few URLs
                    echo "First few URLs:\n";
                    $count = 0;
                    foreach ($urls as $url) {
                        echo "- " . $url->loc . "\n";
                        $count++;
                        if ($count >= 5) {
                            echo "... and " . (count($urls) - 5) . " more\n";
                            break;
                        }
                    }
                }
            }
        } else {
            echo "<span class='error'>This is not a sitemap. ✗</span>\n\n";
        }
        
        // Recommendation
        echo "\nRecommendation:\n";
        if ($isWordPress) {
            echo "<span class='success'>Use type='wordpress' for this URL.</span>\n";
        } elseif ($isSitemap) {
            echo "<span class='success'>Use type='sitemap' for this URL.</span>\n";
        } else {
            echo "<span class='error'>This URL doesn't appear to be a WordPress site or sitemap.</span>\n";
            echo "You may need to find the correct URL for the WordPress site or sitemap.\n";
            
            // Check for common WordPress URLs
            $wpUrls = [
                rtrim($url, '/') . '/wp-json/',
                rtrim($url, '/') . '/sitemap.xml',
                rtrim($url, '/') . '/sitemap_index.xml',
                rtrim($url, '/') . '/wp-sitemap.xml'
            ];
            
            echo "\nTrying common WordPress and sitemap URLs:\n";
            foreach ($wpUrls as $wpUrl) {
                echo "- Testing $wpUrl: ";
                $wpResponse = ContentGrabber::fetchUrl($wpUrl);
                if ($wpResponse) {
                    echo "<span class='success'>Accessible! ✓</span>\n";
                    echo "  Try using this URL instead.\n";
                } else {
                    echo "<span class='error'>Not accessible ✗</span>\n";
                }
            }
        }
    } else {
        echo "<span class='error'>URL is not accessible! ✗</span>\n";
        echo "Please check if the URL is correct and accessible from your server.\n";
    }
    
    echo '</pre>
            </div>
        </div>';
    
    // Add a form to create a job
    if (!empty($url) && ($isWordPress || $isSitemap)) {
        echo '<div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Create a Job</h5>
                </div>
                <div class="card-body">
                    <form action="' . BASE_URL . '/?page=jobs&action=new" method="post">
                        <input type="hidden" name="url" value="' . htmlspecialchars($url) . '">
                        <input type="hidden" name="type" value="' . ($isWordPress ? 'wordpress' : 'sitemap') . '">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Job Name</label>
                            <input type="text" class="form-control" id="name" name="name" value="' . htmlspecialchars(parse_url($url, PHP_URL_HOST)) . '" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="posts_per_run" class="form-label">Posts Per Run</label>
                            <input type="number" class="form-control" id="posts_per_run" name="posts_per_run" value="10" min="1" max="100" required>
                        </div>
                        
                        <button type="submit" class="btn btn-success">Create Job</button>
                    </form>
                </div>
            </div>';
    }
}

echo '    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
?>
