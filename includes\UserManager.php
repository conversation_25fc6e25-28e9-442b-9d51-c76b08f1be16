<?php
/**
 * User Manager Class
 *
 * This class handles user management operations like:
 * - User creation and management
 * - Authentication
 * - Role-based access control
 * - User activity logging
 */
class UserManager {
    private $db;

    // User roles
    const ROLE_ADMIN = 'admin';
    const ROLE_EDITOR = 'editor';
    const ROLE_VIEWER = 'viewer';

    // Role permissions
    private $permissions = [
        self::ROLE_ADMIN => [
            'manage_users' => true,
            'manage_settings' => true,
            'manage_jobs' => true,
            'run_jobs' => true,
            'edit_posts' => true,
            'delete_posts' => true,
            'view_posts' => true,
            'use_api' => true
        ],
        self::ROLE_EDITOR => [
            'manage_users' => false,
            'manage_settings' => false,
            'manage_jobs' => true,
            'run_jobs' => true,
            'edit_posts' => true,
            'delete_posts' => true,
            'view_posts' => true,
            'use_api' => true
        ],
        self::ROLE_VIEWER => [
            'manage_users' => false,
            'manage_settings' => false,
            'manage_jobs' => false,
            'run_jobs' => false,
            'edit_posts' => false,
            'delete_posts' => false,
            'view_posts' => true,
            'use_api' => false
        ]
    ];

    /**
     * Constructor
     *
     * @param Database $db Database instance
     */
    public function __construct(Database $db) {
        $this->db = $db;
    }

    /**
     * Get all users
     *
     * @return array Users
     */
    public function getUsers() {
        return $this->db->query("SELECT id, username, email, role, last_login, created_at FROM users ORDER BY username");
    }

    /**
     * Get a user by ID
     *
     * @param int $userId User ID
     * @return array|bool User data or false if not found
     */
    public function getUser($userId) {
        return $this->db->getRow("SELECT id, username, email, role, last_login, created_at FROM users WHERE id = ?", [$userId]);
    }

    /**
     * Get a user by username
     *
     * @param string $username Username
     * @return array|bool User data or false if not found
     */
    public function getUserByUsername($username) {
        return $this->db->getRow("SELECT * FROM users WHERE username = ?", [$username]);
    }

    /**
     * Get a user by email
     *
     * @param string $email Email
     * @return array|bool User data or false if not found
     */
    public function getUserByEmail($email) {
        return $this->db->getRow("SELECT * FROM users WHERE email = ?", [$email]);
    }

    /**
     * Create a new user
     *
     * @param array $userData User data
     * @return int|bool User ID or false on failure
     */
    public function createUser($userData) {
        // Validate required fields
        $requiredFields = ['username', 'email', 'password'];
        foreach ($requiredFields as $field) {
            if (empty($userData[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Check if username already exists
        $existingUser = $this->getUserByUsername($userData['username']);
        if ($existingUser) {
            throw new Exception("Username already exists");
        }

        // Check if email already exists
        $existingUser = $this->getUserByEmail($userData['email']);
        if ($existingUser) {
            throw new Exception("Email already exists");
        }

        // Set default role if not provided
        if (empty($userData['role'])) {
            $userData['role'] = self::ROLE_VIEWER;
        }

        // Validate role
        if (!in_array($userData['role'], [self::ROLE_ADMIN, self::ROLE_EDITOR, self::ROLE_VIEWER])) {
            throw new Exception("Invalid role");
        }

        // Hash password
        $userData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);

        // Set created_at timestamp
        $userData['created_at'] = date('Y-m-d H:i:s');

        // Insert user
        return $this->db->insert('users', $userData);
    }

    /**
     * Update a user
     *
     * @param int $userId User ID
     * @param array $userData User data
     * @return bool Success status
     */
    public function updateUser($userId, $userData) {
        // Get current user
        $user = $this->getUser($userId);
        if (!$user) {
            throw new Exception("User not found");
        }

        // Check if username is being changed and already exists
        if (!empty($userData['username']) && $userData['username'] !== $user['username']) {
            $existingUser = $this->getUserByUsername($userData['username']);
            if ($existingUser) {
                throw new Exception("Username already exists");
            }
        }

        // Check if email is being changed and already exists
        if (!empty($userData['email']) && $userData['email'] !== $user['email']) {
            $existingUser = $this->getUserByEmail($userData['email']);
            if ($existingUser) {
                throw new Exception("Email already exists");
            }
        }

        // Hash password if provided
        if (!empty($userData['password'])) {
            $userData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);
        }

        // Validate role if provided
        if (!empty($userData['role']) && !in_array($userData['role'], [self::ROLE_ADMIN, self::ROLE_EDITOR, self::ROLE_VIEWER])) {
            throw new Exception("Invalid role");
        }

        // Update user
        return $this->db->update('users', $userData, 'id = ?', [$userId]);
    }

    /**
     * Delete a user
     *
     * @param int $userId User ID
     * @return bool Success status
     */
    public function deleteUser($userId) {
        // Check if user exists
        $user = $this->getUser($userId);
        if (!$user) {
            throw new Exception("User not found");
        }

        // Count total users
        $totalUsers = $this->db->getValue("SELECT COUNT(*) FROM users");

        // Don't allow deleting the last user
        if ($totalUsers <= 1) {
            throw new Exception("Cannot delete the last user");
        }

        // Delete user
        return $this->db->delete('users', 'id = ?', [$userId]);
    }

    /**
     * Authenticate a user
     *
     * @param string $username Username
     * @param string $password Password
     * @return array|bool User data or false if authentication fails
     */
    public function authenticate($username, $password) {
        // Get user by username
        $user = $this->db->getRow("SELECT * FROM users WHERE username = ?", [$username]);

        // Check if user exists and password is correct
        if ($user && password_verify($password, $user['password'])) {
            // Update last login time
            try {
                $this->db->update('users', [
                    'last_login' => date('Y-m-d H:i:s')
                ], 'id = ?', [$user['id']]);
            } catch (Exception $e) {
                // Ignore error if last_login column doesn't exist yet
                // User will need to run the database update script
            }

            // Log activity
            $this->logActivity($user['id'], 'login', 'User logged in');

            return $user;
        }

        return false;
    }

    /**
     * Check if a user has a specific permission
     *
     * @param int $userId User ID
     * @param string $permission Permission name
     * @return bool True if user has permission
     */
    public function hasPermission($userId, $permission) {
        try {
            // Get user
            $user = $this->getUser($userId);
            if (!$user) {
                return false;
            }

            // Get user role
            $role = $user['role'] ?? self::ROLE_VIEWER;

            // Check if role has permission
            return isset($this->permissions[$role][$permission]) && $this->permissions[$role][$permission];
        } catch (Exception $e) {
            // If there's an error (like missing role column), assume admin permission for backward compatibility
            // This allows existing users to access the update page
            return true;
        }
    }

    /**
     * Generate an API key for a user
     *
     * @param int $userId User ID
     * @return string|bool API key or false on failure
     */
    public function generateApiKey($userId) {
        // Check if user exists
        $user = $this->getUser($userId);
        if (!$user) {
            throw new Exception("User not found");
        }

        // Check if user has API permission
        if (!$this->hasPermission($userId, 'use_api')) {
            throw new Exception("User does not have API permission");
        }

        // Generate API key
        $apiKey = bin2hex(random_bytes(32));

        // Update user with API key
        $success = $this->db->update('users', [
            'api_key' => $apiKey
        ], 'id = ?', [$userId]);

        return $success ? $apiKey : false;
    }

    /**
     * Revoke a user's API key
     *
     * @param int $userId User ID
     * @return bool Success status
     */
    public function revokeApiKey($userId) {
        // Check if user exists
        $user = $this->getUser($userId);
        if (!$user) {
            throw new Exception("User not found");
        }

        // Update user to remove API key
        return $this->db->update('users', [
            'api_key' => null
        ], 'id = ?', [$userId]);
    }

    /**
     * Log user activity
     *
     * @param int $userId User ID
     * @param string $action Action performed
     * @param string $details Additional details
     * @return int|bool Activity ID or false on failure
     */
    public function logActivity($userId, $action, $details = '') {
        try {
            // Insert activity log
            return $this->db->insert('user_activity', [
                'user_id' => $userId,
                'action' => $action,
                'details' => $details,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // Ignore error if user_activity table doesn't exist yet
            // User will need to run the database update script
            return false;
        }
    }

    /**
     * Get user activity logs
     *
     * @param int|null $userId User ID (null for all users)
     * @param int $limit Maximum number of logs to return
     * @param int $offset Offset for pagination
     * @return array Activity logs
     */
    public function getActivityLogs($userId = null, $limit = 100, $offset = 0) {
        try {
            $sql = "SELECT a.*, u.username
                    FROM user_activity a
                    JOIN users u ON a.user_id = u.id";
            $params = [];

            if ($userId !== null) {
                $sql .= " WHERE a.user_id = ?";
                $params[] = $userId;
            }

            $sql .= " ORDER BY a.created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;

            return $this->db->query($sql, $params);
        } catch (Exception $e) {
            // Return empty array if user_activity table doesn't exist yet
            return [];
        }
    }
}
