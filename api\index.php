<?php
/**
 * API Entry Point
 * 
 * This file handles all API requests and routes them to the appropriate handler.
 */

// Load configuration
require_once '../config.php';

// Check if the application is installed
if (!is_installed()) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => 'Application not installed'
    ]);
    exit;
}

// Create content directories
create_content_directories();

// Load required files
require_once '../includes/Database.php';
require_once '../includes/ContentGrabber.php';
require_once '../includes/JobManager.php';

// Initialize database connection
$db = new Database();

// Get request method and endpoint
$method = $_SERVER['REQUEST_METHOD'];
$endpoint = isset($_GET['endpoint']) ? $_GET['endpoint'] : '';

// Get request data
$data = [];
if ($method === 'POST' || $method === 'PUT') {
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        $data = $_POST;
    }
}

// API authentication
$authenticated = false;
$apiKey = isset($_SERVER['HTTP_X_API_KEY']) ? $_SERVER['HTTP_X_API_KEY'] : '';

if (!empty($apiKey)) {
    // Check if API key is valid
    $user = $db->getRow("SELECT * FROM users WHERE api_key = ?", [$apiKey]);
    if ($user) {
        $authenticated = true;
    }
}

// Set response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Check authentication for protected endpoints
$publicEndpoints = ['status'];
if (!$authenticated && !in_array($endpoint, $publicEndpoints)) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Unauthorized'
    ]);
    exit;
}

// Route the request to the appropriate handler
switch ($endpoint) {
    case 'status':
        // API status endpoint
        echo json_encode([
            'success' => true,
            'status' => 'API is running',
            'version' => APP_VERSION
        ]);
        break;
        
    case 'jobs':
        require_once 'jobs.php';
        break;
        
    case 'posts':
        require_once 'posts.php';
        break;
        
    case 'grab':
        require_once 'grab.php';
        break;
        
    default:
        // Unknown endpoint
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Endpoint not found'
        ]);
        break;
}
