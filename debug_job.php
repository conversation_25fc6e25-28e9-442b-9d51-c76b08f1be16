<?php
/**
 * Job Debugging Script
 *
 * This script helps debug issues with job execution.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Check if the application is installed
if (!is_installed()) {
    echo "Application not installed. Please run the setup wizard first.\n";
    exit(1);
}

// Create content directories
create_content_directories();

// Load required files
require_once 'includes/Database.php';
require_once 'includes/ContentGrabber.php';
require_once 'includes/WordPressGrabber.php';
require_once 'includes/SitemapGrabber.php';
require_once 'includes/JobManager.php';

// Initialize database connection
$db = new Database();

// Get job ID from query parameter
$jobId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($jobId <= 0) {
    echo "<h1>Job Debugger</h1>";
    echo "<p>Please provide a job ID using the 'id' query parameter.</p>";

    // List available jobs
    $jobs = $db->query("SELECT id, name, url, type FROM jobs ORDER BY id");

    if (!empty($jobs)) {
        echo "<h2>Available Jobs:</h2>";
        echo "<ul>";
        foreach ($jobs as $job) {
            echo "<li><a href='debug_job.php?id={$job['id']}'>{$job['name']} (ID: {$job['id']}, Type: {$job['type']}, URL: {$job['url']})</a></li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No jobs found in the database.</p>";
    }

    exit;
}

// Initialize JobManager
$jobManager = new JobManager($db);

// Get job data
$job = $jobManager->getJob($jobId);

if (!$job) {
    echo "<h1>Job Debugger</h1>";
    echo "<p>Job with ID $jobId not found.</p>";
    exit;
}

echo "<h1>Debugging Job: " . htmlspecialchars($job['name']) . " (ID: $jobId)</h1>";
echo "<pre>";

// Output job details
echo "Job Details:\n";
echo "------------\n";
echo "Name: " . $job['name'] . "\n";
echo "URL: " . $job['url'] . "\n";
echo "Type: " . $job['type'] . "\n";
echo "Status: " . $job['status'] . "\n";
echo "Posts Per Run: " . $job['posts_per_run'] . "\n";
echo "Last Run: " . ($job['last_run'] ? $job['last_run'] : 'Never') . "\n";
echo "Error: " . ($job['error'] ? $job['error'] : 'None') . "\n\n";

// Test URL accessibility
echo "Testing URL accessibility...\n";
$response = ContentGrabber::fetchUrl($job['url']);
if ($response) {
    echo "URL is accessible! ✓\n";
    echo "Response length: " . strlen($response) . " bytes\n";

    // Check if it's a WordPress site
    $isWordPress = ContentGrabber::isWordPressSite($job['url']);
    echo "Is WordPress site: " . ($isWordPress ? "Yes ✓" : "No ✗") . "\n";

    // Check if it's a sitemap
    $isSitemap = ContentGrabber::isSitemap($job['url']);
    echo "Is sitemap: " . ($isSitemap ? "Yes ✓" : "No ✗") . "\n\n";

    // Check if job type matches detected type
    if ($job['type'] === 'wordpress' && !$isWordPress) {
        echo "WARNING: Job type is set to WordPress but the URL doesn't appear to be a WordPress site.\n";
        echo "Consider changing the job type to 'sitemap' if the URL is a sitemap.\n\n";
    } elseif ($job['type'] === 'sitemap' && !$isSitemap) {
        echo "WARNING: Job type is set to sitemap but the URL doesn't appear to be a sitemap.\n";
        echo "Consider changing the job type to 'wordpress' if the URL is a WordPress site.\n\n";
    } else {
        echo "Job type matches detected type. ✓\n\n";
    }
} else {
    echo "ERROR: URL is not accessible! ✗\n";
    echo "Please check if the URL is correct and accessible from your server.\n\n";
}

// Check database tables
echo "Checking database tables...\n";
$requiredTables = ['jobs', 'posts', 'categories', 'tags', 'images', 'post_categories', 'post_tags'];
$optionalTables = ['job_runs', 'user_activity', 'reports'];

foreach ($requiredTables as $table) {
    echo "Table '$table': ";
    try {
        $count = $db->getValue("SELECT COUNT(*) FROM $table");
        echo "Exists ✓ ($count records)\n";
    } catch (Exception $e) {
        echo "MISSING! ✗\n";
        echo "ERROR: " . $e->getMessage() . "\n";
        echo "This is a required table. Please run the setup wizard or check your database.\n";
    }
}

echo "\nOptional tables (added in updates):\n";
foreach ($optionalTables as $table) {
    echo "Table '$table': ";
    try {
        $count = $db->getValue("SELECT COUNT(*) FROM $table");
        echo "Exists ✓ ($count records)\n";
    } catch (Exception $e) {
        echo "Not found ✗ (This is okay, but some features may not work)\n";
    }
}
echo "\n";

// Check content directories
echo "Checking content directories...\n";
$directories = [
    'Content directory' => CONTENT_DIR,
    'Images directory' => IMAGES_DIR,
    'HTML directory' => HTML_DIR,
    'PDF directory' => PDF_DIR
];

foreach ($directories as $name => $dir) {
    echo "$name ($dir): ";
    if (file_exists($dir)) {
        echo "Exists ✓";
        if (is_writable($dir)) {
            echo " (Writable ✓)";
        } else {
            echo " (NOT writable ✗)";
        }
    } else {
        echo "Does NOT exist ✗";
    }
    echo "\n";
}
echo "\n";

// Test grabber creation
echo "Testing grabber creation...\n";
try {
    $grabber = ContentGrabber::create($db, $job['url'], [
        'category' => $job['category'] ?? null,
        'after_date' => $job['after_date'] ?? null,
        'before_date' => $job['before_date'] ?? null
    ]);

    echo "Grabber created successfully! ✓\n";
    echo "Grabber type: " . get_class($grabber) . "\n\n";

    // Test grabbing content
    echo "Testing content grabbing (limited to 1 post)...\n";
    try {
        $posts = $grabber->grab(1);

        if (empty($posts)) {
            echo "No posts found. ✗\n";
            echo "This could be due to:\n";
            echo "- No content matching your filters\n";
            echo "- Issues with the URL structure\n";
            echo "- Content requiring authentication\n\n";
        } else {
            echo "Successfully grabbed " . count($posts) . " post(s)! ✓\n\n";

            // Display post details
            echo "Post details:\n";
            echo "------------\n";
            $post = $posts[0];
            echo "Title: " . $post['title'] . "\n";
            echo "Date: " . $post['date'] . "\n";
            echo "URL: " . $post['link'] . "\n";
            echo "Content length: " . strlen($post['content']) . " bytes\n";
            echo "Images: " . count($post['images']) . "\n";
            echo "Categories: " . count($post['categories']) . "\n";
            echo "Tags: " . count($post['tags']) . "\n\n";

            // Test saving posts
            echo "Testing post saving...\n";
            try {
                // Use reflection to access protected method
                $reflectionMethod = new ReflectionMethod('JobManager', 'savePosts');
                $reflectionMethod->setAccessible(true);

                $savedPosts = $reflectionMethod->invoke($jobManager, $posts, $jobId);

                if (!empty($savedPosts)) {
                    echo "Successfully saved " . count($savedPosts) . " post(s)! ✓\n";
                    echo "Post IDs: " . implode(', ', $savedPosts) . "\n\n";
                } else {
                    echo "No posts were saved. ✗\n\n";
                }
            } catch (Exception $e) {
                echo "ERROR saving posts: " . $e->getMessage() . " ✗\n\n";
            }
        }
    } catch (Exception $e) {
        echo "ERROR grabbing content: " . $e->getMessage() . " ✗\n\n";
    }
} catch (Exception $e) {
    echo "ERROR creating grabber: " . $e->getMessage() . " ✗\n\n";
}

// Run the job
echo "Running the job...\n";
try {
    $result = $jobManager->runJob($jobId);

    if ($result['success']) {
        echo "Job ran successfully! ✓\n";
        echo "Grabbed " . $result['posts_count'] . " posts.\n\n";
    } else {
        echo "Job failed: " . $result['error'] . " ✗\n\n";
    }
} catch (Exception $e) {
    echo "ERROR running job: " . $e->getMessage() . " ✗\n\n";
}

echo "</pre>";

// Add a link back to the jobs page
echo "<p><a href='" . BASE_URL . "/?page=jobs'>Back to Jobs</a></p>";
?>
