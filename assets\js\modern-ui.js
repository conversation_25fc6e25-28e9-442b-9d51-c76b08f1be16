/**
 * Modern UI Enhancements for Content Grabber
 */

// Initialize modern UI features when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeModernUI();
});

function initializeModernUI() {
    // Add loading states to forms
    initializeFormLoading();

    // Add smooth scrolling to anchor links
    initializeSmoothScrolling();

    // Add intersection observer for animations
    initializeScrollAnimations();

    // Add modern tooltips
    initializeTooltips();

    // Add enhanced focus states
    initializeFocusStates();

    // Add loading overlay functionality
    initializeLoadingOverlay();

    // Initialize modern navigation
    initializeModernNavigation();
}

// Form Loading States
function initializeFormLoading() {
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn) {
                // Add loading state
                submitBtn.classList.add('loading');
                submitBtn.disabled = true;

                // Store original text
                const originalText = submitBtn.innerHTML;

                // Add loading spinner
                submitBtn.innerHTML = `
                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Processing...
                `;

                // Restore button after 30 seconds (fallback)
                setTimeout(() => {
                    submitBtn.classList.remove('loading');
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }, 30000);
            }
        });
    });
}

// Smooth Scrolling
function initializeSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;

            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Scroll Animations
function initializeScrollAnimations() {
    const animatedElements = document.querySelectorAll('.fade-in, .fade-in-up, .fade-in-down, .slide-in-left, .slide-in-right, .zoom-in');

    if (animatedElements.length === 0) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    animatedElements.forEach(el => {
        el.style.animationPlayState = 'paused';
        observer.observe(el);
    });
}

// Modern Tooltips
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-bs-toggle="tooltip"]');

    tooltipElements.forEach(element => {
        // Create custom tooltip
        element.addEventListener('mouseenter', function() {
            showTooltip(this);
        });

        element.addEventListener('mouseleave', function() {
            hideTooltip(this);
        });
    });
}

function showTooltip(element) {
    const text = element.getAttribute('title') || element.getAttribute('data-bs-title');
    if (!text) return;

    const tooltip = document.createElement('div');
    tooltip.className = 'modern-tooltip';
    tooltip.textContent = text;

    document.body.appendChild(tooltip);

    // Position tooltip
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';

    // Animate in
    requestAnimationFrame(() => {
        tooltip.classList.add('show');
    });

    element._tooltip = tooltip;
}

function hideTooltip(element) {
    if (element._tooltip) {
        element._tooltip.classList.remove('show');
        setTimeout(() => {
            if (element._tooltip && element._tooltip.parentNode) {
                element._tooltip.parentNode.removeChild(element._tooltip);
            }
            element._tooltip = null;
        }, 200);
    }
}

// Enhanced Focus States
function initializeFocusStates() {
    const focusableElements = document.querySelectorAll('input, textarea, select, button, a, [tabindex]');

    focusableElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.classList.add('focus-ring');
        });

        element.addEventListener('blur', function() {
            this.classList.remove('focus-ring');
        });
    });
}

// Loading Overlay
function initializeLoadingOverlay() {
    // Create loading overlay if it doesn't exist
    if (!document.getElementById('loadingOverlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">Loading...</div>
            </div>
        `;
        document.body.appendChild(overlay);
    }
}

// Global loading functions
window.showLoading = function(text = 'Loading...') {
    const overlay = document.getElementById('loadingOverlay');
    const textElement = overlay.querySelector('.loading-text');

    if (textElement) {
        textElement.textContent = text;
    }

    overlay.classList.add('show');
    document.body.style.overflow = 'hidden';
};

window.hideLoading = function() {
    const overlay = document.getElementById('loadingOverlay');
    overlay.classList.remove('show');
    document.body.style.overflow = '';
};

// Progress bar functionality
window.updateProgress = function(percentage, text = '') {
    let progressBar = document.getElementById('globalProgressBar');

    if (!progressBar) {
        progressBar = document.createElement('div');
        progressBar.id = 'globalProgressBar';
        progressBar.className = 'global-progress-bar';
        progressBar.innerHTML = `
            <div class="progress-fill"></div>
            <div class="progress-text"></div>
        `;
        document.body.appendChild(progressBar);
    }

    const fill = progressBar.querySelector('.progress-fill');
    const textElement = progressBar.querySelector('.progress-text');

    fill.style.width = percentage + '%';
    textElement.textContent = text;

    progressBar.classList.add('show');

    if (percentage >= 100) {
        setTimeout(() => {
            progressBar.classList.remove('show');
        }, 1000);
    }
};

// Notification system
window.showNotification = function(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `modern-notification notification-${type}`;

    const icon = getNotificationIcon(type);
    notification.innerHTML = `
        <div class="notification-content">
            <i class="${icon} notification-icon"></i>
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Add to container or create one
    let container = document.getElementById('notificationContainer');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notificationContainer';
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // Animate in
    requestAnimationFrame(() => {
        notification.classList.add('show');
    });

    // Auto remove
    if (duration > 0) {
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }
};

function getNotificationIcon(type) {
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    return icons[type] || icons.info;
}

// Enhanced table functionality
function initializeModernTables() {
    const tables = document.querySelectorAll('.table');

    tables.forEach(table => {
        // Add hover effects
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.001)';
            });

            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    });
}

// Modern Navigation Enhancement
function initializeModernNavigation() {
    const navbar = document.querySelector('.modern-navbar');
    const mobileToggle = document.querySelector('.mobile-toggle');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (!navbar) return;

    // Navbar scroll effect
    let lastScrollY = window.scrollY;

    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;

        if (currentScrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // Hide/show navbar on scroll
        if (currentScrollY > lastScrollY && currentScrollY > 200) {
            navbar.style.transform = 'translateY(-100%)';
        } else {
            navbar.style.transform = 'translateY(0)';
        }

        lastScrollY = currentScrollY;
    });

    // Mobile toggle functionality
    if (mobileToggle && navbarCollapse) {
        mobileToggle.addEventListener('click', function() {
            const isExpanded = this.getAttribute('aria-expanded') === 'true';
            this.setAttribute('aria-expanded', !isExpanded);

            if (!isExpanded) {
                navbarCollapse.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                navbarCollapse.classList.remove('show');
                document.body.style.overflow = '';
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navbar.contains(e.target) && navbarCollapse.classList.contains('show')) {
                navbarCollapse.classList.remove('show');
                mobileToggle.setAttribute('aria-expanded', 'false');
                document.body.style.overflow = '';
            }
        });

        // Close mobile menu on window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 992 && navbarCollapse.classList.contains('show')) {
                navbarCollapse.classList.remove('show');
                mobileToggle.setAttribute('aria-expanded', 'false');
                document.body.style.overflow = '';
            }
        });
    }

    // Active link highlighting
    const navLinks = document.querySelectorAll('.modern-nav-link');
    const currentPath = window.location.pathname + window.location.search;

    navLinks.forEach(link => {
        const linkPath = new URL(link.href).pathname + new URL(link.href).search;
        if (linkPath === currentPath || (currentPath === '/' && link.href.includes('dashboard'))) {
            link.classList.add('active');
        }
    });

    // Smooth hover effects for nav links
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
        });

        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0)';
            }
        });
    });
}

// Add CSS for scrolled navbar state
const scrolledNavbarCSS = `
.modern-navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(25px) saturate(200%);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 20px 25px -5px rgba(0, 0, 0, 0.15);
}

.modern-navbar.scrolled .brand-icon {
    transform: scale(0.9);
}

.modern-navbar.scrolled .navbar-container {
    height: 70px;
}
`;

// Inject the CSS
const style = document.createElement('style');
style.textContent = scrolledNavbarCSS;
document.head.appendChild(style);

// Initialize tables when DOM is ready
document.addEventListener('DOMContentLoaded', initializeModernTables);
