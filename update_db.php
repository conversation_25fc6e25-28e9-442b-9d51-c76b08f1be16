<?php
/**
 * Database Update Script
 *
 * This script updates the database schema to the latest version.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Start output buffering for web interface
if (php_sapi_name() !== 'cli') {
    ob_start();
}

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/Database.php';

// Define the current database version
$currentDbVersion = '1.1';

// Initialize database connection
$db = new Database();

// Function to safely execute a query
function safeQuery($db, $query, $errorMessage = "Query failed") {
    try {
        return $db->query($query);
    } catch (Exception $e) {
        echo "$errorMessage: " . $e->getMessage() . "\n";
        return false;
    }
}

// Check if job_stats table exists
$jobStatsExists = $db->query("SHOW TABLES LIKE 'job_stats'");
if (empty($jobStatsExists)) {
    echo "Creating job_stats table...\n";

    $db->query("
        CREATE TABLE job_stats (
            id INT(11) NOT NULL AUTO_INCREMENT,
            job_id INT(11) NOT NULL,
            run_date DATETIME NOT NULL,
            total_posts INT(11) NOT NULL DEFAULT 0,
            new_posts INT(11) NOT NULL DEFAULT 0,
            updated_posts INT(11) NOT NULL DEFAULT 0,
            skipped_posts INT(11) NOT NULL DEFAULT 0,
            execution_time FLOAT NOT NULL DEFAULT 0,
            approaches_tried INT(11) NOT NULL DEFAULT 0,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY job_id (job_id),
            KEY run_date (run_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    echo "job_stats table created successfully.\n";
} else {
    echo "job_stats table already exists.\n";
}

// Check if job_runs table exists
$jobRunsExists = $db->query("SHOW TABLES LIKE 'job_runs'");
if (empty($jobRunsExists)) {
    echo "Creating job_runs table...\n";

    $db->query("
        CREATE TABLE job_runs (
            id INT(11) NOT NULL AUTO_INCREMENT,
            job_id INT(11) NOT NULL,
            status ENUM('pending', 'running', 'completed', 'failed') NOT NULL DEFAULT 'pending',
            start_time DATETIME NOT NULL,
            end_time DATETIME DEFAULT NULL,
            posts_grabbed INT(11) NOT NULL DEFAULT 0,
            error TEXT DEFAULT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY job_id (job_id),
            KEY status (status),
            KEY start_time (start_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    echo "job_runs table created successfully.\n";
} else {
    echo "job_runs table already exists.\n";
}

// Check if jobs table has category_id column
$jobsColumns = $db->query("SHOW COLUMNS FROM jobs LIKE 'category_id'");
if (empty($jobsColumns)) {
    echo "Adding category_id column to jobs table...\n";

    $db->query("
        ALTER TABLE jobs
        ADD COLUMN category_id INT(11) DEFAULT NULL AFTER url,
        ADD KEY category_id (category_id)
    ");

    echo "category_id column added to jobs table.\n";
} else {
    echo "category_id column already exists in jobs table.\n";
}

// Check if jobs table has frequency column
$jobsColumns = $db->query("SHOW COLUMNS FROM jobs LIKE 'frequency'");
if (empty($jobsColumns)) {
    echo "Adding frequency column to jobs table...\n";

    $db->query("
        ALTER TABLE jobs
        ADD COLUMN frequency VARCHAR(50) DEFAULT 'daily' AFTER schedule_type
    ");

    echo "frequency column added to jobs table.\n";
} else {
    echo "frequency column already exists in jobs table.\n";
}

// Check if posts table has external_id column
$postsColumns = $db->query("SHOW COLUMNS FROM posts LIKE 'external_id'");
if (empty($postsColumns)) {
    echo "Adding external_id column to posts table...\n";

    $db->query("
        ALTER TABLE posts
        ADD COLUMN external_id VARCHAR(255) DEFAULT NULL AFTER job_id
    ");

    // Add index separately to handle potential errors if data already exists
    try {
        $db->query("ALTER TABLE posts ADD UNIQUE KEY external_id (external_id)");
    } catch (Exception $e) {
        echo "Warning: Could not add unique key to external_id column. This may be due to duplicate data.\n";
    }

    echo "external_id column added to posts table.\n";
} else {
    echo "external_id column already exists in posts table.\n";
}

// Check if jobs table has disable_embed column
$jobsColumns = $db->query("SHOW COLUMNS FROM jobs LIKE 'disable_embed'");
if (empty($jobsColumns)) {
    echo "Adding disable_embed column to jobs table...\n";

    $db->query("
        ALTER TABLE jobs
        ADD COLUMN disable_embed TINYINT(1) NOT NULL DEFAULT 0 AFTER category
    ");

    echo "disable_embed column added to jobs table.\n";
} else {
    echo "disable_embed column already exists in jobs table.\n";
}

// Check if database version is stored
$settingsTable = safeQuery($db, "SHOW TABLES LIKE 'settings'", "Error checking if settings table exists");
if (empty($settingsTable)) {
    echo "Creating settings table...\n";

    // Create the settings table
    $createResult = safeQuery($db, "
        CREATE TABLE settings (
            id INT(11) NOT NULL AUTO_INCREMENT,
            `key` VARCHAR(255) NOT NULL,
            value TEXT,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY `key` (`key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Error creating settings table");

    if ($createResult !== false) {
        // Insert database version
        try {
            $db->insert('settings', [
                'key' => 'db_version',
                'value' => $currentDbVersion
            ]);
            echo "Settings table created and database version set to $currentDbVersion.\n";
        } catch (Exception $e) {
            echo "Error inserting database version: " . $e->getMessage() . "\n";
        }
    }
} else {
    // Check if the db_version key exists
    $dbVersionExists = safeQuery($db, "SELECT 1 FROM settings WHERE `key` = 'db_version'", "Error checking if db_version exists");

    if (!empty($dbVersionExists)) {
        // Update database version
        try {
            $db->update('settings', [
                'value' => $currentDbVersion
            ], '`key` = ?', ['db_version']);
            echo "Database version updated to $currentDbVersion.\n";
        } catch (Exception $e) {
            echo "Error updating database version: " . $e->getMessage() . "\n";
        }
    } else {
        // Insert database version
        try {
            $db->insert('settings', [
                'key' => 'db_version',
                'value' => $currentDbVersion
            ]);
            echo "Database version key created with value $currentDbVersion.\n";
        } catch (Exception $e) {
            echo "Error inserting database version: " . $e->getMessage() . "\n";

            // Try to recreate the settings table if there was an error
            echo "Attempting to recreate settings table...\n";

            // Drop the table if it exists but has issues
            safeQuery($db, "DROP TABLE IF EXISTS settings", "Error dropping settings table");

            // Create the table
            $recreateResult = safeQuery($db, "
                CREATE TABLE settings (
                    id INT(11) NOT NULL AUTO_INCREMENT,
                    `key` VARCHAR(255) NOT NULL,
                    value TEXT,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    UNIQUE KEY `key` (`key`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ", "Error recreating settings table");

            if ($recreateResult !== false) {
                // Insert database version
                try {
                    $db->insert('settings', [
                        'key' => 'db_version',
                        'value' => $currentDbVersion
                    ]);
                    echo "Settings table recreated successfully and database version set to $currentDbVersion.\n";
                } catch (Exception $e2) {
                    echo "Error inserting database version after recreating table: " . $e2->getMessage() . "\n";
                }
            }
        }
    }
}

// Clear the database update notification
unset($_SESSION['db_update_required']);

// Check if this is a web request or command line
if (php_sapi_name() !== 'cli') {
    // This is a web request, output HTML

    // Set page title
    $pageTitle = 'Database Update';

    // Get the update log content
    $updateLog = ob_get_clean();

    // Include tool header
    include_once 'includes/tool_header.php';
    ?>

    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i> Database update completed successfully!
    </div>

    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0">Update Log</h5>
        </div>
        <div class="card-body">
            <div class="update-log" style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; max-height: 400px; overflow-y: auto;">
                <?php echo $updateLog; ?>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-end">
        <a href="<?php echo BASE_URL; ?>" class="btn btn-primary">
            <i class="fas fa-home me-1"></i> Go to Dashboard
        </a>
    </div>

    <?php
    // Include tool footer
    include_once 'includes/tool_footer.php';
} else {
    // This is a command line request
    echo "Database update completed successfully.\n";
}
?>
