<?php
/**
 * Database Update Web Interface
 * 
 * This file provides a web interface to update the database schema.
 */

// Load configuration
require_once 'config.php';

// Check if the application is installed
if (!is_installed()) {
    // Redirect to setup wizard
    header('Location: ' . BASE_URL . '/setup/');
    exit;
}

// Initialize result variables
$success = false;
$error = '';
$output = '';

// Handle update request
if (isset($_POST['update'])) {
    // Start output buffering to capture script output
    ob_start();
    
    try {
        // Include the update script
        include 'update_database.php';
        
        // Set success flag
        $success = true;
    } catch (Exception $e) {
        // Set error message
        $error = $e->getMessage();
    }
    
    // Get output
    $output = ob_get_clean();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Update - Content Grabber</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 40px;
        }
        .update-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .update-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .update-header h1 {
            color: #3498db;
            font-weight: 700;
        }
        .output-container {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="update-container">
            <div class="update-header">
                <h1>Content Grabber Database Update</h1>
                <p class="text-muted">This tool will update your database schema to the latest version</p>
            </div>
            
            <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                Database update completed successfully!
            </div>
            
            <div class="output-container">
                <?php echo htmlspecialchars($output); ?>
            </div>
            
            <div class="mt-4 text-center">
                <a href="<?php echo BASE_URL; ?>" class="btn btn-primary">
                    <i class="fas fa-home me-1"></i> Go to Dashboard
                </a>
            </div>
            <?php elseif ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                Error updating database: <?php echo htmlspecialchars($error); ?>
            </div>
            
            <div class="output-container">
                <?php echo htmlspecialchars($output); ?>
            </div>
            
            <div class="mt-4 text-center">
                <a href="<?php echo BASE_URL; ?>/update.php" class="btn btn-primary">
                    <i class="fas fa-sync me-1"></i> Try Again
                </a>
            </div>
            <?php else: ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Warning:</strong> This will update your database schema. Please make a backup before proceeding.
            </div>
            
            <p>This update will add the following to your database:</p>
            <ul>
                <li>New columns to the users table (role, api_key, remember_token, token_expires, last_login)</li>
                <li>New user_activity table for tracking user actions</li>
                <li>New job_runs table for tracking job execution</li>
                <li>New reports table for storing generated reports</li>
                <li>New metadata and processed columns to the posts table</li>
                <li>New optimized_path column to the images table</li>
            </ul>
            
            <form method="post" class="mt-4">
                <div class="d-grid">
                    <button type="submit" name="update" class="btn btn-primary btn-lg">
                        <i class="fas fa-database me-1"></i> Update Database
                    </button>
                </div>
            </form>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
