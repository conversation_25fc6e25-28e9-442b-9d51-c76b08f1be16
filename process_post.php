<?php
/**
 * Process Post
 *
 * This page handles AI processing of posts.
 */

// Load configuration
require_once 'config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Create content directories
if (function_exists('create_content_directories')) {
    create_content_directories();
}

// Load required files
require_once 'includes/Database.php';
require_once 'includes/AIWorkflowManager.php';
require_once 'includes/AIPostProcessor.php';

// Initialize database connection
$db = new Database();

// Get post ID from query parameter
$postId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($postId <= 0) {
    // Redirect to posts page
    header('Location: ' . BASE_URL . '/?page=posts');
    exit;
}

// Get post data
$post = $db->getRow("SELECT * FROM posts WHERE id = ?", [$postId]);

if (!$post) {
    // Redirect to posts page
    header('Location: ' . BASE_URL . '/?page=posts');
    exit;
}

// Initialize workflow manager
$workflowManager = new AIWorkflowManager($db);

// Get workflows
$workflows = $workflowManager->getWorkflows();
$defaultWorkflow = $workflowManager->getDefaultWorkflow();

// Get current tab
$tab = isset($_GET['tab']) ? $_GET['tab'] : 'overview';

// Get workflow ID
$workflowId = isset($_GET['workflow_id']) ? (int)$_GET['workflow_id'] : ($defaultWorkflow ? $defaultWorkflow['id'] : 0);

// Get language
$language = isset($_GET['language']) ? $_GET['language'] : 'en';

// Get RTL setting
$isRtl = isset($_GET['is_rtl']) ? (bool)$_GET['is_rtl'] : false;

// Get processed post data if available
$processedPost = null;
if ($workflowId > 0) {
    $processedPost = $db->getRow("
        SELECT * FROM ai_processed_posts
        WHERE post_id = ? AND workflow_id = ? AND language = ?
    ", [$postId, $workflowId, $language]);
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    // Get selected workflow
    $selectedWorkflow = $workflowManager->getWorkflow($workflowId);
    if (!$selectedWorkflow) {
        $errorMessage = 'Selected workflow not found.';
    } else {
        // Initialize post processor
        $postProcessor = new AIPostProcessor($db, $post, $selectedWorkflow, $language, $isRtl);

        try {
            switch ($action) {
                case 'process_title':
                    $taskType = $_POST['task_type'] ?? 'translate';
                    $result = $postProcessor->processTitle($taskType);
                    $successMessage = 'Title processed successfully!';
                    break;

                case 'process_content':
                    $taskType = $_POST['task_type'] ?? 'translate';
                    $result = $postProcessor->processContent($taskType);
                    $successMessage = 'Content processed successfully!';
                    break;

                case 'generate_image_prompt':
                    $result = $postProcessor->generateImagePrompt();
                    $successMessage = 'Image prompt generated successfully!';
                    break;

                case 'generate_image':
                    $customPrompt = !empty($_POST['custom_prompt']) ? $_POST['custom_prompt'] : null;
                    $result = $postProcessor->generateImage($customPrompt);
                    $successMessage = 'Image generated successfully!';
                    break;

                case 'process_categories':
                    $result = $postProcessor->processCategories();
                    $successMessage = 'Categories processed successfully!';
                    break;

                case 'process_tags':
                    $result = $postProcessor->processTags();
                    $successMessage = 'Tags processed successfully!';
                    break;

                case 'process_seo':
                    $result = $postProcessor->processSEO();
                    $successMessage = 'SEO metadata processed successfully!';
                    break;

                case 'complete_processing':
                    // Mark processing as completed
                    $postProcessor->updateStatus('completed');
                    $successMessage = 'Processing marked as completed!';
                    break;
            }

            // Refresh processed post data
            $processedPost = $db->getRow("
                SELECT * FROM ai_processed_posts
                WHERE post_id = ? AND workflow_id = ? AND language = ?
            ", [$postId, $workflowId, $language]);
        } catch (Exception $e) {
            $errorMessage = 'Error: ' . $e->getMessage();

            // Update status to failed if there was an error
            if (isset($postProcessor)) {
                $postProcessor->updateStatus('failed');
            }
        }
    }
}

// Set page title
$pageTitle = 'Process Post: ' . htmlspecialchars($post['title']);

// Include header
include_once 'templates/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Process Post</h1>

    <div>
        <a href="<?php echo BASE_URL; ?>/?page=posts" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Posts
        </a>

        <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $postId; ?>" class="btn btn-outline-primary ms-2">
            <i class="fas fa-eye me-1"></i> View Post
        </a>
    </div>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white">
        <h5 class="mb-0">Post Information</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h4><?php echo htmlspecialchars($post['title']); ?></h4>
                <p class="text-muted">
                    <i class="fas fa-calendar me-1"></i> <?php echo date('F j, Y', strtotime($post['date_published'])); ?>
                    <span class="mx-2">|</span>
                    <i class="fas fa-link me-1"></i> <a href="<?php echo htmlspecialchars($post['url']); ?>" target="_blank">Original URL</a>
                </p>

                <?php if (!empty($post['excerpt'])): ?>
                <p><?php echo htmlspecialchars($post['excerpt']); ?></p>
                <?php endif; ?>
            </div>
            <div class="col-md-4">
                <?php if (!empty($post['featured_image'])): ?>
                <img src="<?php echo htmlspecialchars($post['featured_image']); ?>" alt="Featured Image" class="img-fluid rounded">
                <?php else: ?>
                <div class="bg-light rounded p-4 text-center">
                    <i class="fas fa-image fa-3x text-muted"></i>
                    <p class="mt-2 mb-0 text-muted">No featured image</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Processing Settings</h5>
            </div>
            <div class="card-body">
                <form method="get" id="processingSettingsForm">
                    <input type="hidden" name="id" value="<?php echo $postId; ?>">
                    <input type="hidden" name="tab" value="<?php echo $tab; ?>">

                    <div class="mb-3">
                        <label for="workflow_id" class="form-label">Workflow</label>
                        <select class="form-select" id="workflow_id" name="workflow_id" required>
                            <?php foreach ($workflows as $workflow): ?>
                            <option value="<?php echo $workflow['id']; ?>" <?php echo $workflowId === $workflow['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($workflow['name']); ?>
                                <?php echo $workflow['is_default'] ? ' (Default)' : ''; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="language" class="form-label">Target Language</label>
                        <select class="form-select" id="language" name="language">
                            <option value="en" <?php echo $language === 'en' ? 'selected' : ''; ?>>English</option>
                            <option value="es" <?php echo $language === 'es' ? 'selected' : ''; ?>>Spanish</option>
                            <option value="fr" <?php echo $language === 'fr' ? 'selected' : ''; ?>>French</option>
                            <option value="de" <?php echo $language === 'de' ? 'selected' : ''; ?>>German</option>
                            <option value="it" <?php echo $language === 'it' ? 'selected' : ''; ?>>Italian</option>
                            <option value="pt" <?php echo $language === 'pt' ? 'selected' : ''; ?>>Portuguese</option>
                            <option value="ru" <?php echo $language === 'ru' ? 'selected' : ''; ?>>Russian</option>
                            <option value="zh" <?php echo $language === 'zh' ? 'selected' : ''; ?>>Chinese</option>
                            <option value="ja" <?php echo $language === 'ja' ? 'selected' : ''; ?>>Japanese</option>
                            <option value="ko" <?php echo $language === 'ko' ? 'selected' : ''; ?>>Korean</option>
                            <option value="ar" <?php echo $language === 'ar' ? 'selected' : ''; ?>>Arabic</option>
                            <option value="fa" <?php echo $language === 'fa' ? 'selected' : ''; ?>>Farsi</option>
                        </select>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_rtl" name="is_rtl" value="1" <?php echo $isRtl ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_rtl">Right-to-left language</label>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sync-alt me-1"></i> Update Settings
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Processing Status</h5>
            </div>
            <div class="card-body">
                <?php if ($processedPost): ?>
                <div class="mb-3">
                    <label class="form-label">Status</label>
                    <div>
                        <?php if ($processedPost['status'] === 'pending'): ?>
                        <span class="badge bg-secondary">Pending</span>
                        <?php elseif ($processedPost['status'] === 'processing'): ?>
                        <span class="badge bg-warning">Processing</span>
                        <?php elseif ($processedPost['status'] === 'completed'): ?>
                        <span class="badge bg-success">Completed</span>
                        <?php else: ?>
                        <span class="badge bg-danger">Failed</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Started</label>
                    <div><?php echo $processedPost['started_at'] ? date('Y-m-d H:i:s', strtotime($processedPost['started_at'])) : 'Not started'; ?></div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Completed</label>
                    <div><?php echo $processedPost['completed_at'] ? date('Y-m-d H:i:s', strtotime($processedPost['completed_at'])) : 'Not completed'; ?></div>
                </div>

                <div class="progress mb-3">
                    <?php
                    // Calculate progress
                    $progress = 0;
                    $steps = 0;

                    if (!empty($processedPost['processed_title'])) { $progress++; $steps++; }
                    if (!empty($processedPost['processed_content'])) { $progress++; $steps++; }
                    if (!empty($processedPost['image_prompt'])) { $progress++; $steps++; }
                    if (!empty($processedPost['processed_featured_image'])) { $progress++; $steps++; }
                    if (!empty($processedPost['processed_seo_metadata'])) { $progress++; $steps++; }

                    // Ensure we have at least one step
                    $steps = max($steps, 5);
                    $progressPercent = round(($progress / $steps) * 100);
                    ?>
                    <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $progressPercent; ?>%" aria-valuenow="<?php echo $progressPercent; ?>" aria-valuemin="0" aria-valuemax="100"><?php echo $progressPercent; ?>%</div>
                </div>

                <?php if ($processedPost['status'] === 'processing' || $processedPost['status'] === 'pending'): ?>
                <form method="post">
                    <input type="hidden" name="action" value="complete_processing">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-1"></i> Mark as Completed
                    </button>
                </form>
                <?php endif; ?>

                <?php else: ?>
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    No processing has been started for this post with the selected workflow and language.
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="card border-0 shadow-sm">
    <div class="card-header bg-white p-0">
        <ul class="nav nav-tabs card-header-tabs">
            <li class="nav-item">
                <a class="nav-link <?php echo $tab === 'overview' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $postId; ?>&tab=overview&workflow_id=<?php echo $workflowId; ?>&language=<?php echo $language; ?>&is_rtl=<?php echo $isRtl ? '1' : '0'; ?>">
                    <i class="fas fa-home me-1"></i> Overview
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $tab === 'title' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $postId; ?>&tab=title&workflow_id=<?php echo $workflowId; ?>&language=<?php echo $language; ?>&is_rtl=<?php echo $isRtl ? '1' : '0'; ?>">
                    <i class="fas fa-heading me-1"></i> Title
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $tab === 'content' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $postId; ?>&tab=content&workflow_id=<?php echo $workflowId; ?>&language=<?php echo $language; ?>&is_rtl=<?php echo $isRtl ? '1' : '0'; ?>">
                    <i class="fas fa-file-alt me-1"></i> Content
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $tab === 'image' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $postId; ?>&tab=image&workflow_id=<?php echo $workflowId; ?>&language=<?php echo $language; ?>&is_rtl=<?php echo $isRtl ? '1' : '0'; ?>">
                    <i class="fas fa-image me-1"></i> Image
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $tab === 'category' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $postId; ?>&tab=category&workflow_id=<?php echo $workflowId; ?>&language=<?php echo $language; ?>&is_rtl=<?php echo $isRtl ? '1' : '0'; ?>">
                    <i class="fas fa-folder me-1"></i> Category
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $tab === 'tag' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $postId; ?>&tab=tag&workflow_id=<?php echo $workflowId; ?>&language=<?php echo $language; ?>&is_rtl=<?php echo $isRtl ? '1' : '0'; ?>">
                    <i class="fas fa-tags me-1"></i> Tag
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $tab === 'seo' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $postId; ?>&tab=seo&workflow_id=<?php echo $workflowId; ?>&language=<?php echo $language; ?>&is_rtl=<?php echo $isRtl ? '1' : '0'; ?>">
                    <i class="fas fa-search me-1"></i> SEO
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $tab === 'final' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $postId; ?>&tab=final&workflow_id=<?php echo $workflowId; ?>&language=<?php echo $language; ?>&is_rtl=<?php echo $isRtl ? '1' : '0'; ?>">
                    <i class="fas fa-check-circle me-1"></i> Final
                </a>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <?php
        // Display success/error messages
        if (isset($successMessage)) {
            echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
                ' . htmlspecialchars($successMessage) . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
        }

        if (isset($errorMessage)) {
            echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
                ' . htmlspecialchars($errorMessage) . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
        }

        // Include the appropriate tab content
        $tabFile = 'templates/process_post/' . $tab . '.php';
        if (file_exists($tabFile)) {
            include $tabFile;
        } else {
            echo '<div class="alert alert-warning">Tab content not found.</div>';
        }
        ?>
    </div>
</div>

<?php
// Include footer
include_once 'templates/footer.php';
?>
