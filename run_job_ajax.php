<?php
/**
 * Run Job AJAX
 * 
 * This script runs a job in the background and returns the result in JSON format.
 */

// Enable error reporting
ini_set('display_errors', 0); // Disable error display for API
ini_set('display_startup_errors', 0);
error_reporting(0);

// Set content type to JSON
header('Content-Type: application/json');

// Set maximum execution time to 0 (no limit)
set_time_limit(0);

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/Database.php';
require_once 'includes/JobManager.php';

// Initialize database connection
$db = new Database();

// Initialize job manager
$jobManager = new JobManager($db);

// Get job ID from query string
$jobId = isset($_GET['job_id']) ? (int)$_GET['job_id'] : 0;

if (!$jobId) {
    echo json_encode([
        'error' => 'Missing job ID',
        'success' => false
    ]);
    exit;
}

// Run the job
try {
    // Close session to allow concurrent requests
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_write_close();
    }
    
    // Run the job
    $result = $jobManager->runJob($jobId);
    
    // Store stats in session for popup
    if ($result['success']) {
        session_start();
        $_SESSION['job_stats'] = $result['stats'] ?? null;
        $_SESSION['job_id'] = $jobId;
        $_SESSION['job_posts_count'] = $result['posts_count'];
        session_write_close();
    }
    
    // Return result
    echo json_encode($result);
} catch (Exception $e) {
    echo json_encode([
        'error' => $e->getMessage(),
        'success' => false
    ]);
}
