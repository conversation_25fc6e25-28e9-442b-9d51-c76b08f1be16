<?php
// Check if ai_workflows table exists
try {
    $tableExists = $db->query("SHOW TABLES LIKE 'ai_workflows'");

    if (!empty($tableExists)) {
        // Get workflows
        $workflows = $workflowManager->getWorkflows(false);
    } else {
        $workflows = [];
    }
} catch (Exception $e) {
    // Table doesn't exist
    $workflows = [];
}

// Handle edit action
if ($action === 'edit' && $id > 0) {
    $workflow = $workflowManager->getWorkflow($id);
    if (!$workflow) {
        echo '<div class="alert alert-danger">Workflow not found.</div>';
        $action = '';
    } else {
        // Get workflow steps
        $workflowSteps = $workflowManager->getWorkflowSteps($id);

        // Get models for step form
        $models = $db->query("
            SELECT m.*, p.name as provider_name
            FROM ai_models m
            JOIN ai_providers p ON m.provider_id = p.id
            WHERE m.is_active = 1
            ORDER BY p.name, m.name
        ");

        // Handle step edit
        $stepId = isset($_GET['step_id']) ? (int)$_GET['step_id'] : 0;
        if ($stepId > 0) {
            $step = $db->getRow("SELECT * FROM ai_workflow_steps WHERE id = ? AND workflow_id = ?", [$stepId, $id]);
            if (!$step) {
                echo '<div class="alert alert-danger">Workflow step not found.</div>';
                $stepId = 0;
            }
        }
    }
}
?>

<?php if ($action !== 'edit'): ?>
<div class="row">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Add Workflow</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="add_workflow">

                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="form-text">A descriptive name for this workflow</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_default" name="is_default">
                        <label class="form-check-label" for="is_default">Set as default workflow</label>
                    </div>

                    <?php
                    // Check if ai_providers table exists
                    try {
                        $providersTableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
                        $providersCount = !empty($providersTableExists) ? $db->getValue("SELECT COUNT(*) FROM ai_providers") : 0;

                        if (empty($providersTableExists) || $providersCount == 0):
                    ?>
                    <button type="button" class="btn btn-primary" disabled>
                        <i class="fas fa-save me-1"></i> Add Workflow
                    </button>
                    <small class="d-block text-danger mt-2">No AI providers available. Please initialize the AI database first.</small>
                    <?php else: ?>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Add Workflow
                    </button>
                    <?php
                        endif;
                    } catch (Exception $e) {
                        // Table doesn't exist
                    ?>
                    <button type="button" class="btn btn-primary" disabled>
                        <i class="fas fa-save me-1"></i> Add Workflow
                    </button>
                    <small class="d-block text-danger mt-2">AI database tables not found. Please initialize the AI database first.</small>
                    <?php } ?>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Workflows</h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($workflows)): ?>
                <div class="p-4 text-center">
                    <?php
                    // Check if ai_providers table exists
                    try {
                        $providersTableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
                        $providersCount = !empty($providersTableExists) ? $db->getValue("SELECT COUNT(*) FROM ai_providers") : 0;

                        if (empty($providersTableExists) || $providersCount == 0):
                    ?>
                    <div class="alert alert-warning">
                        <p><i class="fas fa-exclamation-triangle me-2"></i> No AI providers found in the database.</p>
                        <a href="<?php echo BASE_URL; ?>/update_ai_tables.php" class="btn btn-primary mt-2">
                            <i class="fas fa-sync-alt me-1"></i> Initialize AI Database
                        </a>
                    </div>
                    <?php else: ?>
                    <p class="text-muted mb-0">No workflows found. Add your first workflow to start using AI features.</p>
                    <?php
                        endif;
                    } catch (Exception $e) {
                        // Table doesn't exist
                    ?>
                    <div class="alert alert-warning">
                        <p><i class="fas fa-exclamation-triangle me-2"></i> AI database tables not found.</p>
                        <a href="<?php echo BASE_URL; ?>/update_ai_tables.php" class="btn btn-primary mt-2">
                            <i class="fas fa-sync-alt me-1"></i> Initialize AI Database
                        </a>
                    </div>
                    <?php } ?>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Steps</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($workflows as $wf): ?>
                            <tr>
                                <td>
                                    <?php echo htmlspecialchars($wf['name']); ?>
                                    <?php if ($wf['is_default']): ?>
                                    <span class="badge bg-primary ms-1">Default</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($wf['description'] ?? ''); ?></td>
                                <td>
                                    <?php
                                    $stepCount = $db->getValue("SELECT COUNT(*) FROM ai_workflow_steps WHERE workflow_id = ?", [$wf['id']]);
                                    echo $stepCount;
                                    ?>
                                </td>
                                <td>
                                    <?php if ($wf['is_active']): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=workflows&action=edit&id=<?php echo $wf['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Edit Workflow: <?php echo htmlspecialchars($workflow['name']); ?></h5>
        <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=workflows" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Workflows
        </a>
    </div>
    <div class="card-body">
        <form method="post" class="mb-4">
            <input type="hidden" name="action" value="update_workflow">
            <input type="hidden" name="workflow_id" value="<?php echo $workflow['id']; ?>">

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($workflow['name']); ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="description" name="description" value="<?php echo htmlspecialchars($workflow['description'] ?? ''); ?>">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_default" name="is_default" <?php echo $workflow['is_default'] ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_default">Set as default workflow</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $workflow['is_active'] ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_active">Active</label>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> Update Workflow
                </button>

                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteWorkflowModal">
                    <i class="fas fa-trash-alt me-1"></i> Delete Workflow
                </button>
            </div>
        </form>

        <hr>

        <h5 class="mb-3">Workflow Steps</h5>

        <?php if ($stepId > 0): ?>
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">Edit Step</h6>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="update_workflow_step">
                    <input type="hidden" name="step_id" value="<?php echo $step['id']; ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="model_id" class="form-label">Model</label>
                                <select class="form-select" id="model_id" name="model_id" required>
                                    <option value="">Select Model</option>
                                    <?php foreach ($models as $model): ?>
                                    <option value="<?php echo $model['id']; ?>" <?php echo ($step['model_id'] === $model['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($model['provider_name'] . ' - ' . $model['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="task_type" class="form-label">Task Type</label>
                                <select class="form-select" id="task_type" name="task_type" required>
                                    <option value="translate" <?php echo ($step['task_type'] === 'translate') ? 'selected' : ''; ?>>Translate</option>
                                    <option value="rewrite" <?php echo ($step['task_type'] === 'rewrite') ? 'selected' : ''; ?>>Rewrite</option>
                                    <option value="translate_rewrite" <?php echo ($step['task_type'] === 'translate_rewrite') ? 'selected' : ''; ?>>Translate & Rewrite</option>
                                    <option value="generate" <?php echo ($step['task_type'] === 'generate') ? 'selected' : ''; ?>>Generate</option>
                                    <option value="optimize" <?php echo ($step['task_type'] === 'optimize') ? 'selected' : ''; ?>>Optimize</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="prompt_template" class="form-label">Prompt Template</label>
                        <textarea class="form-control" id="prompt_template" name="prompt_template" rows="4"><?php echo htmlspecialchars($step['prompt_template'] ?? ''); ?></textarea>
                        <div class="form-text">
                            Use placeholders like {{title}}, {{content}}, {{language}}, etc. that will be replaced with actual values during processing.
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $step['is_active'] ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_active">Active</label>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Update Step
                        </button>

                        <div>
                            <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=workflows&action=edit&id=<?php echo $workflow['id']; ?>" class="btn btn-outline-secondary me-2">
                                Cancel
                            </a>

                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteStepModal">
                                <i class="fas fa-trash-alt me-1"></i> Delete
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <?php else: ?>
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">Add Step</h6>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="add_workflow_step">
                    <input type="hidden" name="workflow_id" value="<?php echo $workflow['id']; ?>">

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="step_type" class="form-label">Step Type</label>
                                <select class="form-select" id="step_type" name="step_type" required>
                                    <option value="title">Title</option>
                                    <option value="content">Content</option>
                                    <option value="image_prompt">Image Prompt</option>
                                    <option value="image_generation">Image Generation</option>
                                    <option value="category">Category</option>
                                    <option value="tag">Tag</option>
                                    <option value="seo">SEO</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="model_id" class="form-label">Model</label>
                                <select class="form-select" id="model_id" name="model_id" required>
                                    <option value="">Select Model</option>
                                    <?php foreach ($models as $model): ?>
                                    <option value="<?php echo $model['id']; ?>">
                                        <?php echo htmlspecialchars($model['provider_name'] . ' - ' . $model['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="task_type" class="form-label">Task Type</label>
                                <select class="form-select" id="task_type" name="task_type" required>
                                    <option value="translate">Translate</option>
                                    <option value="rewrite">Rewrite</option>
                                    <option value="translate_rewrite">Translate & Rewrite</option>
                                    <option value="generate">Generate</option>
                                    <option value="optimize">Optimize</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="prompt_template" class="form-label">Prompt Template</label>
                        <textarea class="form-control" id="prompt_template" name="prompt_template" rows="4"></textarea>
                        <div class="form-text">
                            Use placeholders like {{title}}, {{content}}, {{language}}, etc. that will be replaced with actual values during processing.
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Add Step
                    </button>
                </form>
            </div>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header bg-white">
                <h6 class="mb-0">Current Steps</h6>
            </div>
            <div class="card-body p-0">
                <?php if (empty($workflowSteps)): ?>
                <div class="p-4 text-center">
                    <p class="text-muted mb-0">No steps found. Add steps to define how this workflow processes content.</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Order</th>
                                <th>Step Type</th>
                                <th>Model</th>
                                <th>Task Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($workflowSteps as $step): ?>
                            <tr>
                                <td><?php echo $step['order_index']; ?></td>
                                <td>
                                    <?php
                                    $stepTypeLabels = [
                                        'title' => 'Title',
                                        'content' => 'Content',
                                        'image_prompt' => 'Image Prompt',
                                        'image_generation' => 'Image Generation',
                                        'category' => 'Category',
                                        'tag' => 'Tag',
                                        'seo' => 'SEO'
                                    ];
                                    echo $stepTypeLabels[$step['step_type']] ?? $step['step_type'];
                                    ?>
                                </td>
                                <td><?php echo htmlspecialchars($step['model_name']); ?></td>
                                <td>
                                    <?php
                                    $taskTypeLabels = [
                                        'translate' => 'Translate',
                                        'rewrite' => 'Rewrite',
                                        'translate_rewrite' => 'Translate & Rewrite',
                                        'generate' => 'Generate',
                                        'optimize' => 'Optimize'
                                    ];
                                    echo $taskTypeLabels[$step['task_type']] ?? $step['task_type'];
                                    ?>
                                </td>
                                <td>
                                    <?php if ($step['is_active']): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=workflows&action=edit&id=<?php echo $workflow['id']; ?>&step_id=<?php echo $step['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Delete Workflow Modal -->
<div class="modal fade" id="deleteWorkflowModal" tabindex="-1" aria-labelledby="deleteWorkflowModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteWorkflowModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the workflow "<?php echo htmlspecialchars($workflow['name']); ?>"?</p>
                <p class="text-danger">This action cannot be undone. All steps in this workflow will also be deleted.</p>
                <?php if ($workflow['is_default']): ?>
                <p class="text-danger"><strong>Warning:</strong> This is the default workflow. Deleting it may cause issues with AI processing.</p>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="action" value="delete_workflow">
                    <input type="hidden" name="workflow_id" value="<?php echo $workflow['id']; ?>">
                    <button type="submit" class="btn btn-danger" <?php echo $workflow['is_default'] ? 'disabled' : ''; ?>>Delete Workflow</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php if ($stepId > 0): ?>
<!-- Delete Step Modal -->
<div class="modal fade" id="deleteStepModal" tabindex="-1" aria-labelledby="deleteStepModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteStepModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this workflow step?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="action" value="delete_workflow_step">
                    <input type="hidden" name="step_id" value="<?php echo $step['id']; ?>">
                    <input type="hidden" name="workflow_id" value="<?php echo $workflow['id']; ?>">
                    <button type="submit" class="btn btn-danger">Delete Step</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<?php endif; ?>
