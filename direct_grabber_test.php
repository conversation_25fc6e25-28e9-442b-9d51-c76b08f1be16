<?php
/**
 * Direct WordPress Grabber Test
 * 
 * This script tests the WordPressGrabber class directly with detailed debugging.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Create content directories
create_content_directories();

// Load required files
require_once 'includes/Database.php';
require_once 'includes/ContentGrabber.php';
require_once 'includes/WordPressGrabber.php';

// Initialize database connection
$db = new Database();

// URL to test
$url = isset($_GET['url']) ? $_GET['url'] : 'https://autogpt.net';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 5;
$disable_embed = isset($_GET['disable_embed']) ? (bool)$_GET['disable_embed'] : false;
$debug = isset($_GET['debug']) ? (bool)$_GET['debug'] : true;
$raw_mode = isset($_GET['raw_mode']) ? (bool)$_GET['raw_mode'] : false;

// HTML header
if (!$raw_mode) {
    echo '<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Direct WordPress Grabber Test</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <style>
            pre {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                overflow: auto;
                max-height: 400px;
            }
            .success {
                color: green;
            }
            .error {
                color: red;
            }
            .warning {
                color: orange;
            }
        </style>
    </head>
    <body>
        <div class="container py-5">
            <h1>Direct WordPress Grabber Test</h1>
            <p class="lead">This tool tests the WordPressGrabber class directly with detailed debugging.</p>
            
            <form method="get" class="mb-4">
                <div class="mb-3">
                    <label for="url" class="form-label">WordPress URL</label>
                    <input type="url" class="form-control" id="url" name="url" value="' . htmlspecialchars($url) . '" required>
                </div>
                <div class="mb-3">
                    <label for="limit" class="form-label">Number of posts to grab</label>
                    <input type="number" class="form-control" id="limit" name="limit" value="' . $limit . '" min="1" max="100">
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="disable_embed" name="disable_embed" value="1" ' . ($disable_embed ? 'checked' : '') . '>
                    <label class="form-check-label" for="disable_embed">Disable _embed parameter</label>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="debug" name="debug" value="1" ' . ($debug ? 'checked' : '') . '>
                    <label class="form-check-label" for="debug">Enable debug mode</label>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="raw_mode" name="raw_mode" value="1" ' . ($raw_mode ? 'checked' : '') . '>
                    <label class="form-check-label" for="raw_mode">Raw mode (no HTML)</label>
                </div>
                <button type="submit" class="btn btn-primary">Test Grabber</button>
            </form>';
}

// Start output buffer for raw mode
if ($raw_mode) {
    ob_start();
}

echo "Testing WordPress Grabber with URL: $url\n";
echo "Limit: $limit posts\n";
echo "Disable _embed: " . ($disable_embed ? 'Yes' : 'No') . "\n";
echo "Debug mode: " . ($debug ? 'Enabled' : 'Disabled') . "\n\n";

// Create a custom error handler to capture all errors
if ($debug) {
    set_error_handler(function($errno, $errstr, $errfile, $errline) {
        echo "PHP ERROR [$errno]: $errstr in $errfile on line $errline\n";
        return true;
    });
}

try {
    // Create grabber with debug mode
    echo "Creating WordPress grabber...\n";
    $options = [
        'debug' => $debug,
        'disable_embed' => $disable_embed
    ];
    
    // Create a subclass of WordPressGrabber to expose protected methods
    class DebugWordPressGrabber extends WordPressGrabber {
        public function publicFetchUrl($url) {
            return self::fetchUrl($url);
        }
        
        public function publicProcessPost($post) {
            return $this->processPost($post);
        }
    }
    
    $grabber = new DebugWordPressGrabber($db, $url, $options);
    
    // Test URL accessibility
    echo "Testing URL accessibility...\n";
    $response = $grabber->publicFetchUrl($url);
    if ($response) {
        echo "URL is accessible! ✓\n";
        echo "Response length: " . strlen($response) . " bytes\n\n";
    } else {
        echo "URL is not accessible! ✗\n\n";
    }
    
    // Test WordPress API
    echo "Testing WordPress API...\n";
    $apiUrl = rtrim($url, '/') . '/wp-json/';
    $apiResponse = $grabber->publicFetchUrl($apiUrl);
    if ($apiResponse) {
        echo "WordPress API is accessible! ✓\n";
        echo "Response length: " . strlen($apiResponse) . " bytes\n\n";
    } else {
        echo "WordPress API is not accessible! ✗\n\n";
    }
    
    // Test posts endpoint
    echo "Testing posts endpoint...\n";
    $postsUrl = rtrim($url, '/') . '/wp-json/wp/v2/posts?per_page=1';
    $postsResponse = $grabber->publicFetchUrl($postsUrl);
    if ($postsResponse) {
        echo "Posts endpoint is accessible! ✓\n";
        echo "Response length: " . strlen($postsResponse) . " bytes\n\n";
        
        // Parse response
        $postsData = json_decode($postsResponse, true);
        if (is_array($postsData) && !empty($postsData)) {
            echo "Found " . count($postsData) . " posts! ✓\n\n";
            
            // Test processing a post
            echo "Testing post processing...\n";
            $post = $postsData[0];
            try {
                $processedPost = $grabber->publicProcessPost($post);
                if ($processedPost) {
                    echo "Post processed successfully! ✓\n";
                    echo "Processed post ID: " . $processedPost['id'] . "\n";
                    echo "Title: " . $processedPost['title'] . "\n";
                    echo "Date: " . $processedPost['date'] . "\n";
                    echo "Is future: " . ($processedPost['is_future'] ? 'Yes' : 'No') . "\n";
                    echo "Images: " . count($processedPost['images']) . "\n\n";
                } else {
                    echo "Post processing failed! ✗\n\n";
                }
            } catch (Exception $e) {
                echo "Error processing post: " . $e->getMessage() . " ✗\n\n";
            }
        } else {
            echo "No posts found or invalid JSON! ✗\n\n";
        }
    } else {
        echo "Posts endpoint is not accessible! ✗\n\n";
    }
    
    // Grab posts
    echo "Grabbing $limit posts...\n";
    $startTime = microtime(true);
    $posts = $grabber->grab($limit);
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);
    
    if (empty($posts)) {
        echo "No posts grabbed! ✗\n";
        echo "Execution time: $executionTime seconds\n\n";
    } else {
        echo "Successfully grabbed " . count($posts) . " posts in $executionTime seconds! ✓\n\n";
        
        // Display post details
        echo "Post details:\n";
        echo "------------\n";
        foreach ($posts as $index => $post) {
            echo "Post #" . ($index + 1) . ":\n";
            echo "  ID: " . $post['id'] . "\n";
            echo "  Title: " . $post['title'] . "\n";
            echo "  Date: " . $post['date'] . "\n";
            echo "  Is future: " . ($post['is_future'] ? 'Yes' : 'No') . "\n";
            echo "  Link: " . $post['link'] . "\n";
            echo "  Content length: " . strlen($post['content']) . " bytes\n";
            echo "  Images: " . count($post['images']) . "\n";
            echo "  Categories: " . count($post['categories']) . "\n";
            echo "  Tags: " . count($post['tags']) . "\n";
            echo "\n";
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . " ✗\n";
    echo "File: " . $e->getFile() . " on line " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Restore error handler
if ($debug) {
    restore_error_handler();
}

// End output buffer for raw mode
if ($raw_mode) {
    $output = ob_get_clean();
    header('Content-Type: text/plain');
    echo $output;
    exit;
}

// HTML footer
echo '</div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
?>
