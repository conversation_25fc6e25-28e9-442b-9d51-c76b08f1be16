// Content Grabber JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // Add confirmation for delete actions
    var deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });
    
    // Handle URL type selection
    var typeSelect = document.getElementById('type');
    var urlInput = document.getElementById('url');
    
    if (typeSelect && urlInput) {
        typeSelect.addEventListener('change', function() {
            var placeholder = '';
            var helpText = '';
            
            if (this.value === 'wordpress') {
                placeholder = 'https://example.com';
                helpText = 'Enter the WordPress site URL';
            } else if (this.value === 'sitemap') {
                placeholder = 'https://example.com/sitemap.xml';
                helpText = 'Enter the sitemap URL';
            }
            
            urlInput.placeholder = placeholder;
            document.querySelector('.form-text').textContent = helpText;
        });
    }
    
    // Handle content editor
    var contentTextarea = document.getElementById('content');
    if (contentTextarea) {
        // Add simple formatting buttons
        var editorToolbar = document.createElement('div');
        editorToolbar.className = 'btn-toolbar mb-2';
        editorToolbar.innerHTML = `
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" data-format="bold" title="Bold">
                    <i class="fas fa-bold"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" data-format="italic" title="Italic">
                    <i class="fas fa-italic"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" data-format="link" title="Link">
                    <i class="fas fa-link"></i>
                </button>
            </div>
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" data-format="h2" title="Heading 2">
                    <i class="fas fa-heading"></i>2
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" data-format="h3" title="Heading 3">
                    <i class="fas fa-heading"></i>3
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" data-format="p" title="Paragraph">
                    <i class="fas fa-paragraph"></i>
                </button>
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-secondary" data-format="ul" title="Unordered List">
                    <i class="fas fa-list-ul"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" data-format="ol" title="Ordered List">
                    <i class="fas fa-list-ol"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" data-format="blockquote" title="Blockquote">
                    <i class="fas fa-quote-right"></i>
                </button>
            </div>
        `;
        
        contentTextarea.parentNode.insertBefore(editorToolbar, contentTextarea);
        
        // Add event listeners to formatting buttons
        var formatButtons = document.querySelectorAll('[data-format]');
        formatButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var format = this.getAttribute('data-format');
                var textarea = document.getElementById('content');
                var start = textarea.selectionStart;
                var end = textarea.selectionEnd;
                var selectedText = textarea.value.substring(start, end);
                var replacement = '';
                
                switch (format) {
                    case 'bold':
                        replacement = '<strong>' + selectedText + '</strong>';
                        break;
                    case 'italic':
                        replacement = '<em>' + selectedText + '</em>';
                        break;
                    case 'link':
                        var url = prompt('Enter URL:', 'http://');
                        if (url) {
                            replacement = '<a href="' + url + '">' + (selectedText || url) + '</a>';
                        } else {
                            return;
                        }
                        break;
                    case 'h2':
                        replacement = '<h2>' + selectedText + '</h2>';
                        break;
                    case 'h3':
                        replacement = '<h3>' + selectedText + '</h3>';
                        break;
                    case 'p':
                        replacement = '<p>' + selectedText + '</p>';
                        break;
                    case 'ul':
                        replacement = '<ul>\n  <li>' + selectedText.split('\n').join('</li>\n  <li>') + '</li>\n</ul>';
                        break;
                    case 'ol':
                        replacement = '<ol>\n  <li>' + selectedText.split('\n').join('</li>\n  <li>') + '</li>\n</ol>';
                        break;
                    case 'blockquote':
                        replacement = '<blockquote>' + selectedText + '</blockquote>';
                        break;
                }
                
                textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);
                textarea.focus();
                textarea.selectionStart = start;
                textarea.selectionEnd = start + replacement.length;
            });
        });
    }
});
