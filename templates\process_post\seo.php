<div class="row">
    <div class="col-md-6">
        <h4 class="mb-3">Original SEO Data</h4>
        <div class="card mb-4">
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Title</label>
                    <div><?php echo htmlspecialchars($post['title']); ?></div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">URL</label>
                    <div><?php echo htmlspecialchars($post['url']); ?></div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Slug</label>
                    <div><?php echo htmlspecialchars($post['slug']); ?></div>
                </div>
                
                <?php if (!empty($post['excerpt'])): ?>
                <div class="mb-3">
                    <label class="form-label">Excerpt</label>
                    <div><?php echo htmlspecialchars($post['excerpt']); ?></div>
                </div>
                <?php endif; ?>
                
                <?php
                // Get categories
                $categories = $db->query("
                    SELECT c.name 
                    FROM post_categories pc
                    JOIN categories c ON pc.category_id = c.id
                    WHERE pc.post_id = ?
                ", [$post['id']]);
                
                if (!empty($categories)):
                ?>
                <div class="mb-3">
                    <label class="form-label">Categories</label>
                    <div>
                        <?php foreach ($categories as $category): ?>
                        <span class="badge bg-primary me-1"><?php echo htmlspecialchars($category['name']); ?></span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php
                // Get tags
                $tags = $db->query("
                    SELECT t.name 
                    FROM post_tags pt
                    JOIN tags t ON pt.tag_id = t.id
                    WHERE pt.post_id = ?
                ", [$post['id']]);
                
                if (!empty($tags)):
                ?>
                <div class="mb-3">
                    <label class="form-label">Tags</label>
                    <div>
                        <?php foreach ($tags as $tag): ?>
                        <span class="badge bg-secondary me-1"><?php echo htmlspecialchars($tag['name']); ?></span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php
                // Get metadata
                $metadata = !empty($post['metadata']) ? json_decode($post['metadata'], true) : [];
                
                if (!empty($metadata)):
                ?>
                <div class="mb-3">
                    <label class="form-label">Metadata</label>
                    <div>
                        <?php foreach ($metadata as $key => $value): ?>
                        <div><strong><?php echo htmlspecialchars($key); ?>:</strong> <?php echo htmlspecialchars($value); ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <h4 class="mb-3">Process SEO</h4>
        <div class="card mb-4">
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="process_seo">
                    
                    <p class="text-muted">Generate SEO metadata, categories, and tags based on the post content.</p>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i> Process SEO
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <h4 class="mb-3">Processed SEO Data</h4>
        
        <?php if ($processedPost && !empty($processedPost['processed_seo_metadata'])): ?>
        <div class="card mb-4">
            <div class="card-body">
                <?php
                // Try to parse the SEO metadata
                $seoData = [];
                $seoText = $processedPost['processed_seo_metadata'];
                
                // Look for meta description
                if (preg_match('/meta\s+description:(.+?)(?:\n|$)/i', $seoText, $matches)) {
                    $seoData['meta_description'] = trim($matches[1]);
                }
                
                // Look for keywords
                if (preg_match('/keywords:(.+?)(?:\n|$)/i', $seoText, $matches)) {
                    $keywords = array_map('trim', explode(',', $matches[1]));
                    $seoData['keywords'] = $keywords;
                }
                
                // Look for slug
                if (preg_match('/slug:(.+?)(?:\n|$)/i', $seoText, $matches)) {
                    $seoData['slug'] = trim($matches[1]);
                }
                
                // Look for categories
                if (preg_match('/categories:(.+?)(?:\n|$)/i', $seoText, $matches)) {
                    $categories = array_map('trim', explode(',', $matches[1]));
                    $seoData['categories'] = $categories;
                }
                
                // Look for tags
                if (preg_match('/tags:(.+?)(?:\n|$)/i', $seoText, $matches)) {
                    $tags = array_map('trim', explode(',', $matches[1]));
                    $seoData['tags'] = $tags;
                }
                ?>
                
                <?php if (!empty($seoData['meta_description'])): ?>
                <div class="mb-3">
                    <label class="form-label">Meta Description</label>
                    <div class="<?php echo $isRtl ? 'text-end' : ''; ?>" dir="<?php echo $isRtl ? 'rtl' : 'ltr'; ?>">
                        <?php echo htmlspecialchars($seoData['meta_description']); ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($seoData['keywords'])): ?>
                <div class="mb-3">
                    <label class="form-label">Keywords</label>
                    <div>
                        <?php foreach ($seoData['keywords'] as $keyword): ?>
                        <span class="badge bg-info me-1"><?php echo htmlspecialchars($keyword); ?></span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($seoData['slug'])): ?>
                <div class="mb-3">
                    <label class="form-label">Slug</label>
                    <div><?php echo htmlspecialchars($seoData['slug']); ?></div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($seoData['categories'])): ?>
                <div class="mb-3">
                    <label class="form-label">Categories</label>
                    <div>
                        <?php foreach ($seoData['categories'] as $category): ?>
                        <span class="badge bg-primary me-1"><?php echo htmlspecialchars($category); ?></span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($seoData['tags'])): ?>
                <div class="mb-3">
                    <label class="form-label">Tags</label>
                    <div>
                        <?php foreach ($seoData['tags'] as $tag): ?>
                        <span class="badge bg-secondary me-1"><?php echo htmlspecialchars($tag); ?></span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="mb-3">
                    <label class="form-label">Raw SEO Data</label>
                    <pre class="bg-light p-3 rounded"><?php echo htmlspecialchars($processedPost['processed_seo_metadata']); ?></pre>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            SEO data has been processed successfully.
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            No processed SEO data available. Use the form on the left to process SEO data.
        </div>
        <?php endif; ?>
    </div>
</div>
