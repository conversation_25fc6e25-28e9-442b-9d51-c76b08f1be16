<?php
// Get processing logs if available
$logs = [];
if ($processedPost) {
    $logs = $db->query("
        SELECT l.*, m.name as model_name, p.name as provider_name
        FROM ai_processing_logs l
        JOIN ai_models m ON l.model_id = m.id
        JOIN ai_providers p ON m.provider_id = p.id
        WHERE l.processed_post_id = ?
        ORDER BY l.created_at DESC
    ", [$processedPost['id']]);
}
?>

<div class="row">
    <div class="col-md-6">
        <h4 class="mb-3">Processing Overview</h4>

        <?php if (!$processedPost): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            No processing has been started for this post with the selected workflow and language.
            <p class="mt-2 mb-0">Use the tabs above to start processing different aspects of the post.</p>
        </div>
        <?php else: ?>
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Processing Status</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Title</span>
                        <?php if (!empty($processedPost['processed_title'])): ?>
                        <span class="badge bg-success">Processed</span>
                        <?php else: ?>
                        <span class="badge bg-secondary">Pending</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Content</span>
                        <?php if (!empty($processedPost['processed_content'])): ?>
                        <span class="badge bg-success">Processed</span>
                        <?php else: ?>
                        <span class="badge bg-secondary">Pending</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Image Prompt</span>
                        <?php if (!empty($processedPost['image_prompt'])): ?>
                        <span class="badge bg-success">Generated</span>
                        <?php else: ?>
                        <span class="badge bg-secondary">Pending</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Feature Image</span>
                        <?php if (!empty($processedPost['processed_featured_image'])): ?>
                        <span class="badge bg-success">Generated</span>
                        <?php else: ?>
                        <span class="badge bg-secondary">Pending</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Categories</span>
                        <?php if (!empty($processedPost['processed_categories'])): ?>
                        <span class="badge bg-success">Processed</span>
                        <?php else: ?>
                        <span class="badge bg-secondary">Pending</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Tags</span>
                        <?php if (!empty($processedPost['processed_tags'])): ?>
                        <span class="badge bg-success">Processed</span>
                        <?php else: ?>
                        <span class="badge bg-secondary">Pending</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>SEO Metadata</span>
                        <?php if (!empty($processedPost['processed_seo_metadata'])): ?>
                        <span class="badge bg-success">Processed</span>
                        <?php else: ?>
                        <span class="badge bg-secondary">Pending</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info">
            <i class="fas fa-lightbulb me-2"></i>
            <strong>Tip:</strong> Use the tabs above to process different aspects of the post.
            <p class="mt-2 mb-0">Once you've processed all the aspects you need, go to the "Final" tab to see the complete processed post.</p>
        </div>
        <?php endif; ?>
    </div>

    <div class="col-md-6">
        <h4 class="mb-3">Processing Logs</h4>

        <?php if (empty($logs)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            No processing logs available.
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Step</th>
                        <th>Model</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($logs as $log): ?>
                    <tr>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></td>
                        <td>
                            <?php
                            $stepTypeLabels = [
                                'title' => 'Title',
                                'content' => 'Content',
                                'image_prompt' => 'Image Prompt',
                                'image_generation' => 'Image Generation',
                                'category' => 'Category',
                                'tag' => 'Tag',
                                'seo' => 'SEO'
                            ];
                            echo $stepTypeLabels[$log['step_type']] ?? $log['step_type'];

                            echo ' (';
                            $taskTypeLabels = [
                                'translate' => 'Translate',
                                'rewrite' => 'Rewrite',
                                'translate_rewrite' => 'Translate & Rewrite',
                                'generate' => 'Generate',
                                'optimize' => 'Optimize'
                            ];
                            echo $taskTypeLabels[$log['task_type']] ?? $log['task_type'];
                            echo ')';
                            ?>
                        </td>
                        <td><?php echo htmlspecialchars($log['provider_name'] . ' - ' . $log['model_name']); ?></td>
                        <td>
                            <?php if ($log['status'] === 'success'): ?>
                            <span class="badge bg-success">Success</span>
                            <?php else: ?>
                            <span class="badge bg-danger">Failed</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>
