<?php
/**
 * Login Page
 *
 * This file handles user authentication.
 */

// Load configuration
require_once 'config.php';

// Check if the application is installed
if (!is_installed()) {
    // Redirect to setup wizard
    header('Location: ' . BASE_URL . '/setup/');
    exit;
}

// Start session
session_start();

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    // Redirect to dashboard
    header('Location: ' . BASE_URL . '/');
    exit;
}

// Handle login form submission
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);

    // Validate inputs
    if (empty($username) || empty($password)) {
        $error = 'Username and password are required.';
    } else {
        // Connect to database
        require_once 'includes/Database.php';
        $db = new Database();

        // Get user by username
        $user = $db->getRow("SELECT * FROM users WHERE username = ?", [$username]);

        if ($user && password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];

            // Set remember me cookie if requested
            if ($remember) {
                // Generate a unique token
                $token = bin2hex(random_bytes(32));

                // Store token in database
                $db->update('users', [
                    'remember_token' => $token,
                    'token_expires' => date('Y-m-d H:i:s', strtotime('+30 days'))
                ], 'id = ?', [$user['id']]);

                // Set cookie
                setcookie('remember_token', $token, time() + (86400 * 30), '/'); // 30 days
            }

            // Update last login time
            try {
                $db->update('users', [
                    'last_login' => date('Y-m-d H:i:s')
                ], 'id = ?', [$user['id']]);
            } catch (Exception $e) {
                // Ignore error if last_login column doesn't exist yet
                // User will need to run the database update script
            }

            // Redirect to dashboard
            header('Location: ' . BASE_URL . '/');
            exit;
        } else {
            $error = 'Invalid username or password.';
        }
    }
}

// Check for remember me cookie
if (isset($_COOKIE['remember_token'])) {
    $token = $_COOKIE['remember_token'];

    // Connect to database
    require_once 'includes/Database.php';
    $db = new Database();

    // Get user by token
    $user = $db->getRow("SELECT * FROM users WHERE remember_token = ? AND token_expires > NOW()", [$token]);

    if ($user) {
        // Set session variables
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];

        // Update last login time
        $db->update('users', [
            'last_login' => date('Y-m-d H:i:s')
        ], 'id = ?', [$user['id']]);

        // Redirect to dashboard
        header('Location: ' . BASE_URL . '/');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Content Grabber</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 20px;
        }
        .login-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .login-header {
            background-color: #3498db;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .login-body {
            padding: 20px;
        }
        .login-footer {
            background-color: #f8f9fa;
            padding: 15px 20px;
            text-align: center;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="card login-card">
            <div class="login-header">
                <h3 class="mb-0">
                    <i class="fas fa-globe me-2"></i>
                    Content Grabber
                </h3>
                <p class="mb-0">Login to your account</p>
            </div>

            <div class="login-body">
                <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>

                <form method="post">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">Remember me</label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i> Login
                        </button>
                    </div>
                </form>
            </div>

            <div class="login-footer">
                <p class="mb-0">Content Grabber v<?php echo APP_VERSION; ?></p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
