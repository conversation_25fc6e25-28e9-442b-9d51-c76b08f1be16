<?php
// Set page title
$pageTitle = 'Settings';

// Get action
$action = $_GET['action'] ?? '';

// Handle action
if ($action === 'cleanup') {
    // Redirect to cleanup page
    header('Location: ' . BASE_URL . '/cleanup.php');
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'update_settings':
            // Update settings
            $postsPerPage = (int)($_POST['posts_per_page'] ?? 10);
            $maxPostsToGrab = (int)($_POST['max_posts_to_grab'] ?? 100);

            // Update config file
            $configContent = file_get_contents(BASE_PATH . '/config.php');
            $configContent = preg_replace('/define\(\'POSTS_PER_PAGE\', \d+\);/', "define('POSTS_PER_PAGE', $postsPerPage);", $configContent);
            $configContent = preg_replace('/define\(\'MAX_POSTS_TO_GRAB\', \d+\);/', "define('MAX_POSTS_TO_GRAB', $maxPostsToGrab);", $configContent);

            file_put_contents(BASE_PATH . '/config.php', $configContent);

            $successMessage = 'Settings updated successfully!';
            break;

        case 'update_user':
            // Update user
            $username = $_POST['username'] ?? '';
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';

            // Validate inputs
            if (empty($username) || empty($email)) {
                $errorMessage = 'Username and email are required.';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errorMessage = 'Invalid email address.';
            } elseif (!empty($password) && $password !== $confirmPassword) {
                $errorMessage = 'Passwords do not match.';
            } else {
                // Get current user
                $user = $db->getRow("SELECT * FROM users LIMIT 1");

                if ($user) {
                    // Update user data
                    $userData = [
                        'username' => $username,
                        'email' => $email
                    ];

                    // Update password if provided
                    if (!empty($password)) {
                        $userData['password'] = password_hash($password, PASSWORD_DEFAULT);
                    }

                    $db->update('users', $userData, 'id = ?', [$user['id']]);
                    $successMessage = 'User updated successfully!';
                } else {
                    $errorMessage = 'User not found.';
                }
            }
            break;

        case 'generate_api_key':
            // Generate API key
            $apiKey = bin2hex(random_bytes(32)); // 64 character hex string

            // Update user with API key
            $db->update('users', [
                'api_key' => $apiKey
            ], 'id = ?', [$_SESSION['user_id']]);

            $successMessage = 'API key generated successfully!';

            // Refresh user data
            $user = $db->getRow("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);
            break;

        case 'regenerate_api_key':
            // Regenerate API key
            $apiKey = bin2hex(random_bytes(32)); // 64 character hex string

            // Update user with new API key
            $db->update('users', [
                'api_key' => $apiKey
            ], 'id = ?', [$_SESSION['user_id']]);

            $successMessage = 'API key regenerated successfully!';

            // Refresh user data
            $user = $db->getRow("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);
            break;

        case 'revoke_api_key':
            // Revoke API key
            $db->update('users', [
                'api_key' => null
            ], 'id = ?', [$_SESSION['user_id']]);

            $successMessage = 'API key revoked successfully!';

            // Refresh user data
            $user = $db->getRow("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);
            break;

        case 'clear_cache':
            // Clear cache
            $cacheType = $_POST['cache_type'] ?? '';

            switch ($cacheType) {
                case 'images':
                    // Delete all images
                    array_map('unlink', glob(IMAGES_DIR . '/*/*.jpg'));
                    array_map('unlink', glob(IMAGES_DIR . '/*/*.jpeg'));
                    array_map('unlink', glob(IMAGES_DIR . '/*/*.png'));
                    array_map('unlink', glob(IMAGES_DIR . '/*/*.gif'));

                    $successMessage = 'Image cache cleared successfully!';
                    break;

                case 'html':
                    // Delete all HTML files
                    array_map('unlink', glob(HTML_DIR . '/*.html'));

                    $successMessage = 'HTML cache cleared successfully!';
                    break;

                case 'pdf':
                    // Delete all PDF files
                    array_map('unlink', glob(PDF_DIR . '/*.pdf'));

                    $successMessage = 'PDF cache cleared successfully!';
                    break;

                case 'all':
                    // Delete all cached files
                    array_map('unlink', glob(IMAGES_DIR . '/*/*.jpg'));
                    array_map('unlink', glob(IMAGES_DIR . '/*/*.jpeg'));
                    array_map('unlink', glob(IMAGES_DIR . '/*/*.png'));
                    array_map('unlink', glob(IMAGES_DIR . '/*/*.gif'));
                    array_map('unlink', glob(HTML_DIR . '/*.html'));
                    array_map('unlink', glob(PDF_DIR . '/*.pdf'));

                    $successMessage = 'All cache cleared successfully!';
                    break;
            }
            break;
    }
}

// Display success/error messages
if (isset($successMessage)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($successMessage) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

if (isset($errorMessage)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($errorMessage) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

// Get current user
$user = $db->getRow("SELECT * FROM users LIMIT 1");
?>

<div class="row">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">General Settings</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="update_settings">

                    <div class="mb-3">
                        <label for="posts_per_page" class="form-label">Posts Per Page</label>
                        <input type="number" class="form-control" id="posts_per_page" name="posts_per_page" value="<?php echo POSTS_PER_PAGE; ?>" min="1" max="100" required>
                        <div class="form-text">Number of posts to display per page</div>
                    </div>

                    <div class="mb-3">
                        <label for="max_posts_to_grab" class="form-label">Max Posts to Grab</label>
                        <input type="number" class="form-control" id="max_posts_to_grab" name="max_posts_to_grab" value="<?php echo MAX_POSTS_TO_GRAB; ?>" min="1" max="1000" required>
                        <div class="form-text">Maximum number of posts to grab in a single job run</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Save Settings
                    </button>
                </form>
            </div>
        </div>

        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Cache Management</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="clear_cache">

                    <div class="mb-3">
                        <label for="cache_type" class="form-label">Cache Type</label>
                        <select class="form-select" id="cache_type" name="cache_type" required>
                            <option value="images">Images</option>
                            <option value="html">HTML Files</option>
                            <option value="pdf">PDF Files</option>
                            <option value="all">All Cache</option>
                        </select>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Warning: This action cannot be undone. All cached files of the selected type will be permanently deleted.
                    </div>

                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> Clear Cache
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">User Settings</h5>
            </div>
            <div class="card-body">
                <?php if ($user): ?>
                <form method="post">
                    <input type="hidden" name="action" value="update_user">

                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="password" name="password">
                        <div class="form-text">Leave empty to keep current password</div>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Update User
                    </button>
                </form>

                <hr class="my-4">

                <h5>API Access</h5>
                <p>Use the API key to access the Content Grabber API programmatically.</p>

                <?php
                // Check if user has an API key
                $apiKey = $user['api_key'] ?? '';
                if (empty($apiKey)):
                ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    You don't have an API key yet. Generate one to use the API.
                </div>

                <form method="post">
                    <input type="hidden" name="action" value="generate_api_key">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-key me-1"></i> Generate API Key
                    </button>
                </form>
                <?php else: ?>
                <div class="mb-3">
                    <label for="api_key" class="form-label">Your API Key</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="api_key" value="<?php echo htmlspecialchars($apiKey); ?>" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyApiKey()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="form-text">Keep this key secret. It provides full access to your account via the API.</div>
                </div>

                <div class="d-flex">
                    <form method="post" class="me-2">
                        <input type="hidden" name="action" value="regenerate_api_key">
                        <button type="submit" class="btn btn-warning" onclick="return confirm('Are you sure you want to regenerate your API key? This will invalidate your current key.')">
                            <i class="fas fa-sync me-1"></i> Regenerate Key
                        </button>
                    </form>

                    <form method="post">
                        <input type="hidden" name="action" value="revoke_api_key">
                        <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to revoke your API key? You will no longer be able to use the API until you generate a new key.')">
                            <i class="fas fa-trash me-1"></i> Revoke Key
                        </button>
                    </form>
                </div>

                <script>
                function copyApiKey() {
                    var apiKeyInput = document.getElementById('api_key');
                    apiKeyInput.select();
                    document.execCommand('copy');
                    alert('API key copied to clipboard!');
                }
                </script>
                <?php endif; ?>
                <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    No user found. Please run the setup wizard again.
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Diagnostic Tools</h5>
            </div>
            <div class="card-body">
                <p>These tools help diagnose and fix issues with content grabbing:</p>

                <div class="list-group mb-3">
                    <a href="fix_database.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-database me-2"></i> Fix Database Schema
                            <small class="d-block text-muted">Update database schema to support all features</small>
                        </div>
                        <span class="badge bg-primary rounded-pill">
                            <i class="fas fa-arrow-right"></i>
                        </span>
                    </a>

                    <a href="web_run_job.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-play me-2"></i> Web Job Runner
                            <small class="d-block text-muted">Run jobs directly from your browser</small>
                        </div>
                        <span class="badge bg-primary rounded-pill">
                            <i class="fas fa-arrow-right"></i>
                        </span>
                    </a>

                    <a href="update_db.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-database me-2"></i> Update Database
                            <small class="d-block text-muted">Run database updates to ensure compatibility</small>
                        </div>
                        <span class="badge bg-primary rounded-pill">
                            <i class="fas fa-arrow-right"></i>
                        </span>
                    </a>

                    <a href="<?php echo BASE_URL; ?>/?page=settings&action=cleanup" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-broom me-2"></i> Data Cleanup
                            <small class="d-block text-muted">Clean up posts, images, and related data</small>
                        </div>
                        <span class="badge bg-primary rounded-pill">
                            <i class="fas fa-arrow-right"></i>
                        </span>
                    </a>
                </div>
            </div>
        </div>

        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">System Information</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>PHP Version:</th>
                        <td><?php echo PHP_VERSION; ?></td>
                    </tr>
                    <tr>
                        <th>Database:</th>
                        <td>MySQL</td>
                    </tr>
                    <tr>
                        <th>Application Version:</th>
                        <td><?php echo APP_VERSION; ?></td>
                    </tr>
                    <tr>
                        <th>Base Path:</th>
                        <td><?php echo BASE_PATH; ?></td>
                    </tr>
                    <tr>
                        <th>Base URL:</th>
                        <td><?php echo BASE_URL; ?></td>
                    </tr>
                    <tr>
                        <th>Images Directory:</th>
                        <td><?php echo IMAGES_DIR; ?></td>
                    </tr>
                    <tr>
                        <th>HTML Directory:</th>
                        <td><?php echo HTML_DIR; ?></td>
                    </tr>
                    <tr>
                        <th>PDF Directory:</th>
                        <td><?php echo PDF_DIR; ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
