<?php
/**
 * AI Post Processor
 *
 * This class handles AI processing of posts.
 */
class AIPostProcessor {
    private $db;
    private $post;
    private $workflow;
    private $processedPostId;
    private $language;
    private $isRtl;

    /**
     * Constructor
     *
     * @param Database $db Database instance
     * @param array $post Post data
     * @param array $workflow Workflow data
     * @param string $language Target language
     * @param bool $isRtl Whether the target language is RTL
     */
    public function __construct(Database $db, $post, $workflow, $language = 'en', $isRtl = false) {
        $this->db = $db;
        $this->post = $post;
        $this->workflow = $workflow;
        $this->language = $language;
        $this->isRtl = $isRtl;

        // Create or get processed post record
        $this->initProcessedPost();
    }

    /**
     * Initialize processed post record
     */
    private function initProcessedPost() {
        // Check if processed post already exists
        $processedPost = $this->db->getRow(
            "SELECT * FROM ai_processed_posts WHERE post_id = ? AND workflow_id = ? AND language = ?",
            [$this->post['id'], $this->workflow['id'], $this->language]
        );

        if ($processedPost) {
            $this->processedPostId = $processedPost['id'];
        } else {
            // Create new processed post record
            $this->processedPostId = $this->db->insert('ai_processed_posts', [
                'post_id' => $this->post['id'],
                'workflow_id' => $this->workflow['id'],
                'status' => 'pending',
                'language' => $this->language,
                'is_rtl' => $this->isRtl ? 1 : 0
            ]);
        }
    }

    /**
     * Process post title
     *
     * @param string $taskType Task type (translate, rewrite, translate_rewrite)
     * @return array Processing result
     */
    public function processTitle($taskType = 'translate') {
        // Update status
        $this->updateStatus('processing');

        // Get workflow step for title processing
        $steps = $this->getWorkflowManager()->getWorkflowSteps($this->workflow['id'], 'title');
        if (empty($steps)) {
            throw new Exception("No title processing step found in workflow");
        }

        // Find step matching the task type
        $step = null;
        foreach ($steps as $s) {
            if ($s['task_type'] === $taskType) {
                $step = $s;
                break;
            }
        }

        // If no matching step, use the first one
        if (!$step) {
            $step = $steps[0];
        }

        // Get AI provider
        $provider = $this->getAIProvider($step['provider_slug']);

        // Prepare prompt
        $prompt = $this->preparePrompt($step['prompt_template'], [
            'title' => $this->post['title'],
            'language' => $this->language
        ]);

        // Generate text
        $result = $provider->generateText($prompt, [
            'model_id' => $step['model_id'],
            'processed_post_id' => $this->processedPostId,
            'step_type' => 'title',
            'task_type' => $taskType
        ]);

        // Update processed post with generated title
        $this->db->update('ai_processed_posts',
            ['processed_title' => $result['text']],
            'id = ?',
            [$this->processedPostId]
        );

        return [
            'original_title' => $this->post['title'],
            'processed_title' => $result['text'],
            'task_type' => $taskType,
            'model' => $step['model_name'],
            'execution_time' => $result['execution_time']
        ];
    }

    /**
     * Process post content
     *
     * @param string $taskType Task type (translate, rewrite, translate_rewrite)
     * @return array Processing result
     */
    public function processContent($taskType = 'translate') {
        // Update status
        $this->updateStatus('processing');

        // Get workflow step for content processing
        $steps = $this->getWorkflowManager()->getWorkflowSteps($this->workflow['id'], 'content');
        if (empty($steps)) {
            throw new Exception("No content processing step found in workflow");
        }

        // Find step matching the task type
        $step = null;
        foreach ($steps as $s) {
            if ($s['task_type'] === $taskType) {
                $step = $s;
                break;
            }
        }

        // If no matching step, use the first one
        if (!$step) {
            $step = $steps[0];
        }

        // Get AI provider
        $provider = $this->getAIProvider($step['provider_slug']);

        // Prepare prompt
        $prompt = $this->preparePrompt($step['prompt_template'], [
            'content' => $this->post['content'],
            'title' => $this->post['title'],
            'language' => $this->language
        ]);

        // Generate text
        $result = $provider->generateText($prompt, [
            'model_id' => $step['model_id'],
            'processed_post_id' => $this->processedPostId,
            'step_type' => 'content',
            'task_type' => $taskType
        ]);

        // Update processed post with generated content
        $this->db->update('ai_processed_posts',
            ['processed_content' => $result['text']],
            'id = ?',
            [$this->processedPostId]
        );

        return [
            'original_content_length' => strlen($this->post['content']),
            'processed_content_length' => strlen($result['text']),
            'task_type' => $taskType,
            'model' => $step['model_name'],
            'execution_time' => $result['execution_time']
        ];
    }

    /**
     * Generate image prompt
     *
     * @return array Processing result
     */
    public function generateImagePrompt() {
        // Update status
        $this->updateStatus('processing');

        // Get workflow step for image prompt generation
        $steps = $this->getWorkflowManager()->getWorkflowSteps($this->workflow['id'], 'image_prompt');
        if (empty($steps)) {
            throw new Exception("No image prompt generation step found in workflow");
        }

        $step = $steps[0];

        // Get AI provider
        $provider = $this->getAIProvider($step['provider_slug']);

        // Prepare prompt
        $prompt = $this->preparePrompt($step['prompt_template'], [
            'title' => $this->post['title'],
            'excerpt' => $this->post['excerpt'] ?? substr(strip_tags($this->post['content']), 0, 500),
            'language' => $this->language
        ]);

        // Generate text
        $result = $provider->generateText($prompt, [
            'model_id' => $step['model_id'],
            'processed_post_id' => $this->processedPostId,
            'step_type' => 'image_prompt',
            'task_type' => 'generate'
        ]);

        // Update processed post with generated image prompt
        $this->db->update('ai_processed_posts',
            ['image_prompt' => $result['text']],
            'id = ?',
            [$this->processedPostId]
        );

        return [
            'image_prompt' => $result['text'],
            'model' => $step['model_name'],
            'execution_time' => $result['execution_time']
        ];
    }

    /**
     * Generate image
     *
     * @param string $prompt Optional custom prompt (if not provided, will use stored image prompt)
     * @return array Processing result
     */
    public function generateImage($prompt = null) {
        // Update status
        $this->updateStatus('processing');

        // Get workflow step for image generation
        $steps = $this->getWorkflowManager()->getWorkflowSteps($this->workflow['id'], 'image_generation');
        if (empty($steps)) {
            throw new Exception("No image generation step found in workflow");
        }

        $step = $steps[0];

        // Get AI provider
        $provider = $this->getAIProvider($step['provider_slug']);

        // Get prompt
        if (!$prompt) {
            $processedPost = $this->getProcessedPost();
            $prompt = $processedPost['image_prompt'];

            if (empty($prompt)) {
                // Generate image prompt first
                $promptResult = $this->generateImagePrompt();
                $prompt = $promptResult['image_prompt'];
            }
        }

        // Generate image
        $result = $provider->generateImage($prompt, [
            'model_id' => $step['model_id'],
            'processed_post_id' => $this->processedPostId,
            'step_type' => 'image_generation',
            'task_type' => 'generate'
        ]);

        // Download and save the image
        $imageUrl = $result['images'][0]['url'] ?? null;
        $localPath = null;

        if ($imageUrl) {
            $localPath = $this->downloadImage($imageUrl);

            // Update processed post with generated image
            $this->db->update('ai_processed_posts',
                ['processed_featured_image' => $localPath],
                'id = ?',
                [$this->processedPostId]
            );
        }

        return [
            'image_url' => $imageUrl,
            'local_path' => $localPath,
            'model' => $step['model_name'],
            'execution_time' => $result['execution_time']
        ];
    }

    /**
     * Process categories
     *
     * @return array Processing result
     */
    public function processCategories() {
        // Update status
        $this->updateStatus('processing');

        // Get workflow step for category processing
        $steps = $this->getWorkflowManager()->getWorkflowSteps($this->workflow['id'], 'category');
        if (empty($steps)) {
            throw new Exception("No category processing step found in workflow");
        }

        $step = $steps[0];

        // Get AI provider
        $provider = $this->getAIProvider($step['provider_slug']);

        // Prepare prompt
        $prompt = $this->preparePrompt($step['prompt_template'], [
            'title' => $this->post['title'],
            'excerpt' => $this->post['excerpt'] ?? substr(strip_tags($this->post['content']), 0, 500),
            'language' => $this->language
        ]);

        // Generate text
        $result = $provider->generateText($prompt, [
            'model_id' => $step['model_id'],
            'processed_post_id' => $this->processedPostId,
            'step_type' => 'category',
            'task_type' => 'generate'
        ]);

        // Update processed post with generated categories
        $this->db->update('ai_processed_posts',
            ['processed_categories' => $result['text']],
            'id = ?',
            [$this->processedPostId]
        );

        return [
            'categories' => $result['text'],
            'model' => $step['model_name'],
            'execution_time' => $result['execution_time']
        ];
    }

    /**
     * Process tags
     *
     * @return array Processing result
     */
    public function processTags() {
        // Update status
        $this->updateStatus('processing');

        // Get workflow step for tag processing
        $steps = $this->getWorkflowManager()->getWorkflowSteps($this->workflow['id'], 'tag');
        if (empty($steps)) {
            throw new Exception("No tag processing step found in workflow");
        }

        $step = $steps[0];

        // Get AI provider
        $provider = $this->getAIProvider($step['provider_slug']);

        // Prepare prompt
        $prompt = $this->preparePrompt($step['prompt_template'], [
            'title' => $this->post['title'],
            'excerpt' => $this->post['excerpt'] ?? substr(strip_tags($this->post['content']), 0, 500),
            'language' => $this->language
        ]);

        // Generate text
        $result = $provider->generateText($prompt, [
            'model_id' => $step['model_id'],
            'processed_post_id' => $this->processedPostId,
            'step_type' => 'tag',
            'task_type' => 'generate'
        ]);

        // Update processed post with generated tags
        $this->db->update('ai_processed_posts',
            ['processed_tags' => $result['text']],
            'id = ?',
            [$this->processedPostId]
        );

        return [
            'tags' => $result['text'],
            'model' => $step['model_name'],
            'execution_time' => $result['execution_time']
        ];
    }

    /**
     * Process SEO metadata
     *
     * @return array Processing result
     */
    public function processSEO() {
        // Update status
        $this->updateStatus('processing');

        // Get workflow step for SEO processing
        $steps = $this->getWorkflowManager()->getWorkflowSteps($this->workflow['id'], 'seo');
        if (empty($steps)) {
            throw new Exception("No SEO processing step found in workflow");
        }

        $step = $steps[0];

        // Get AI provider
        $provider = $this->getAIProvider($step['provider_slug']);

        // Prepare prompt
        $prompt = $this->preparePrompt($step['prompt_template'], [
            'title' => $this->post['title'],
            'excerpt' => $this->post['excerpt'] ?? substr(strip_tags($this->post['content']), 0, 500),
            'language' => $this->language
        ]);

        // Generate text
        $result = $provider->generateText($prompt, [
            'model_id' => $step['model_id'],
            'processed_post_id' => $this->processedPostId,
            'step_type' => 'seo',
            'task_type' => 'optimize'
        ]);

        // Update processed post with generated SEO metadata
        $this->db->update('ai_processed_posts',
            ['processed_seo_metadata' => $result['text']],
            'id = ?',
            [$this->processedPostId]
        );

        return [
            'seo_metadata' => $result['text'],
            'model' => $step['model_name'],
            'execution_time' => $result['execution_time']
        ];
    }

    /**
     * Get processed post data
     *
     * @return array Processed post data
     */
    public function getProcessedPost() {
        return $this->db->getRow("SELECT * FROM ai_processed_posts WHERE id = ?", [$this->processedPostId]);
    }

    /**
     * Update processed post status
     *
     * @param string $status New status
     * @return bool Success status
     */
    public function updateStatus($status) {
        $data = ['status' => $status];

        if ($status === 'processing' && empty($this->db->getValue("SELECT started_at FROM ai_processed_posts WHERE id = ?", [$this->processedPostId]))) {
            $data['started_at'] = date('Y-m-d H:i:s');
        } else if ($status === 'completed' || $status === 'failed') {
            $data['completed_at'] = date('Y-m-d H:i:s');
        }

        return $this->db->update('ai_processed_posts', $data, 'id = ?', [$this->processedPostId]);
    }

    /**
     * Download image from URL
     *
     * @param string $url Image URL
     * @return string Local path to downloaded image
     */
    private function downloadImage($url) {
        // Create directory for AI-generated images
        $aiImagesDir = IMAGES_DIR . '/ai_generated';
        if (!is_dir($aiImagesDir)) {
            mkdir($aiImagesDir, 0755, true);
        }

        // Generate filename
        $filename = 'ai_' . $this->post['id'] . '_' . $this->processedPostId . '_' . time() . '.jpg';
        $localPath = $aiImagesDir . '/' . $filename;

        // Download image
        $imageData = file_get_contents($url);
        if ($imageData === false) {
            throw new Exception("Failed to download image from URL: $url");
        }

        // Save image
        if (file_put_contents($localPath, $imageData) === false) {
            throw new Exception("Failed to save image to path: $localPath");
        }

        return $localPath;
    }

    /**
     * Prepare prompt by replacing placeholders
     *
     * @param string $template Prompt template
     * @param array $data Data to replace placeholders
     * @return string Prepared prompt
     */
    private function preparePrompt($template, $data) {
        $prompt = $template;

        foreach ($data as $key => $value) {
            $prompt = str_replace('{{' . $key . '}}', $value, $prompt);
        }

        return $prompt;
    }

    /**
     * Get AI provider instance
     *
     * @param string $providerSlug Provider slug
     * @return AIProvider AI provider instance
     */
    private function getAIProvider($providerSlug) {
        require_once __DIR__ . '/AIProvider.php';
        return AIProvider::create($this->db, $providerSlug);
    }

    /**
     * Get workflow manager instance
     *
     * @return AIWorkflowManager Workflow manager instance
     */
    private function getWorkflowManager() {
        require_once __DIR__ . '/AIWorkflowManager.php';
        return new AIWorkflowManager($this->db);
    }
}
?>
