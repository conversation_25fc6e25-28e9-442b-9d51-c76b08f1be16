<?php
/**
 * Add is_future Column to Posts Table
 * 
 * This script adds the is_future column to the posts table if it doesn't exist.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// Check if the is_future column exists
try {
    $db->query("SELECT is_future FROM posts LIMIT 1");
    echo "The is_future column already exists in the posts table.\n";
} catch (Exception $e) {
    // Column doesn't exist, add it
    echo "Adding is_future column to posts table...\n";
    
    try {
        // Start transaction
        $db->beginTransaction();
        
        // Add is_future column to posts table
        $db->query("ALTER TABLE posts ADD COLUMN is_future TINYINT(1) NOT NULL DEFAULT 0");
        
        // Update existing posts to set is_future based on date_published
        $db->query("UPDATE posts SET is_future = IF(date_published > NOW(), 1, 0)");
        
        // Commit transaction
        $db->commit();
        
        echo "The is_future column has been added to the posts table successfully!\n";
    } catch (Exception $e) {
        // Rollback transaction
        $db->rollback();
        
        echo "Error adding is_future column: " . $e->getMessage() . "\n";
    }
}

// Now let's run the job to test if it works
echo "\nRunning job to test if it works...\n";

// Load JobManager
require_once 'includes/JobManager.php';
$jobManager = new JobManager($db);

// Get all jobs
$jobs = $jobManager->getJobs();

if (empty($jobs)) {
    echo "No jobs found. Please create a job first.\n";
    exit;
}

// Display jobs
echo "Available jobs:\n";
foreach ($jobs as $job) {
    echo "- ID: {$job['id']}, Name: {$job['name']}, URL: {$job['url']}\n";
}

// Ask for job ID
echo "\nEnter the ID of the job you want to run: ";
$jobId = trim(fgets(STDIN));

// Run the job
echo "Running job $jobId...\n";
$result = $jobManager->runJob($jobId);

// Display result
if ($result['success']) {
    echo "Job ran successfully! Grabbed {$result['posts_count']} posts.\n";
} else {
    echo "Job failed: {$result['error']}\n";
}

// Display posts
$posts = $db->query("SELECT * FROM posts WHERE job_id = ? ORDER BY date_published DESC", [$jobId]);

if (empty($posts)) {
    echo "No posts found for this job.\n";
    exit;
}

echo "\nPosts grabbed:\n";
foreach ($posts as $post) {
    $futureStatus = $post['is_future'] ? 'FUTURE' : 'CURRENT';
    echo "- ID: {$post['id']}, Title: {$post['title']}, Date: {$post['date_published']} ($futureStatus)\n";
}
?>
