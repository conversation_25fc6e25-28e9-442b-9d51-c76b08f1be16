<?php
/**
 * Web-based Job Runner
 *
 * This script runs a job directly from the web browser.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/Database.php';
require_once 'includes/JobManager.php';

// Initialize database connection
$db = new Database();

// Initialize JobManager
$jobManager = new JobManager($db);

// Set page title
$pageTitle = 'Web Job Runner';

// Include tool header
include_once 'includes/tool_header.php';

// Add custom styles
?>
<style>
    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow: auto;
        font-size: 14px;
        line-height: 1.5;
    }
    .success {
        color: green;
        font-weight: bold;
    }
    .error {
        color: red;
        font-weight: bold;
    }
</style>
<?php

// Check if job ID is provided
$jobId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($jobId <= 0) {
    // Get all jobs
    $jobs = $jobManager->getJobs();

    if (empty($jobs)) {
        echo '<div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            No jobs found. Please create a job first.
        </div>';
    } else {
        // Display jobs
        echo '<div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Available Jobs</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>URL</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>';

        foreach ($jobs as $job) {
            echo '<tr>
                <td>' . $job['id'] . '</td>
                <td>' . htmlspecialchars($job['name']) . '</td>
                <td><a href="' . htmlspecialchars($job['url']) . '" target="_blank">' . htmlspecialchars($job['url']) . '</a></td>
                <td>' . $job['type'] . '</td>
                <td>' . $job['status'] . '</td>
                <td>
                    <a href="web_run_job.php?id=' . $job['id'] . '" class="btn btn-primary btn-sm">Run Job</a>
                </td>
            </tr>';
        }

        echo '</tbody>
                    </table>
                </div>
            </div>
        </div>';
    }
} else {
    // Get job
    $job = $jobManager->getJob($jobId);

    if (!$job) {
        echo '<div class="alert alert-danger">
            <i class="fas fa-times-circle me-2"></i>
            Job with ID ' . $jobId . ' not found.
        </div>';
    } else {
        echo '<div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Running Job: ' . htmlspecialchars($job['name']) . ' (ID: ' . $job['id'] . ')</h5>
            </div>
            <div class="card-body">
                <p><strong>URL:</strong> <a href="' . htmlspecialchars($job['url']) . '" target="_blank">' . htmlspecialchars($job['url']) . '</a></p>
                <p><strong>Type:</strong> ' . $job['type'] . '</p>
                <p><strong>Posts per run:</strong> ' . $job['posts_per_run'] . '</p>
                <p><strong>Status:</strong> <span id="job-status">' . $job['status'] . '</span></p>';

        if (isset($job['disable_embed']) && $job['disable_embed']) {
            echo '<p><strong>Disable _embed:</strong> Yes</p>';
        } else {
            echo '<p><strong>Disable _embed:</strong> No</p>';
        }

        // Add progress bar
        echo '<div class="progress mb-3" style="height: 25px;">
            <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
        </div>

        <div id="progress-details" class="mb-3">
            <div class="row">
                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title">Processed</h5>
                            <p id="processed-count" class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title">New</h5>
                            <p id="new-count" class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h5 class="card-title">Updated</h5>
                            <p id="updated-count" class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-secondary">
                        <div class="card-body text-center">
                            <h5 class="card-title">Skipped</h5>
                            <p id="skipped-count" class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="current-post" class="alert alert-info mb-3" style="display: none;">
            <strong>Currently processing:</strong> <span id="current-post-title"></span>
        </div>

        <pre id="job-log">';

        // Add JavaScript for progress updates
        echo '<script>
            // Function to update progress
            function updateProgress() {
                fetch("get_job_progress.php?job_id=' . $jobId . '")
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === "running") {
                            // Update progress bar
                            const progress = data.progress || {};
                            const progressPercent = progress.progress || 0;
                            document.getElementById("progress-bar").style.width = progressPercent + "%";
                            document.getElementById("progress-bar").textContent = progressPercent + "%";
                            document.getElementById("progress-bar").setAttribute("aria-valuenow", progressPercent);

                            // Update counters
                            document.getElementById("processed-count").textContent = progress.processed || 0;
                            document.getElementById("new-count").textContent = progress.new || 0;
                            document.getElementById("updated-count").textContent = progress.updated || 0;
                            document.getElementById("skipped-count").textContent = progress.skipped || 0;

                            // Update current post
                            if (progress.post_title) {
                                document.getElementById("current-post").style.display = "block";
                                document.getElementById("current-post-title").textContent = progress.post_title;
                            }

                            // Update status
                            document.getElementById("job-status").textContent = "Running";

                            // Continue polling
                            setTimeout(updateProgress, 1000);
                        } else if (data.status === "completed") {
                            // Job completed, reload page to show results
                            location.reload();
                        }
                    })
                    .catch(error => {
                        console.error("Error fetching progress:", error);
                        // Continue polling even on error
                        setTimeout(updateProgress, 2000);
                    });
            }

            // Start progress updates
            setTimeout(updateProgress, 1000);
        </script>';

        // Run the job
        echo "Running job...\n";

        // Start the job in a separate process to allow for progress updates
        session_write_close(); // Close the session to allow concurrent requests

        // Run the job
        $result = $jobManager->runJob($jobId);

        // Display result
        if ($result['success']) {
            echo "<span class='success'>Job ran successfully! Grabbed {$result['posts_count']} posts.</span>\n";
        } else {
            echo "<span class='error'>Job failed: {$result['error']}</span>\n";
        }

        // Display posts
        $posts = $db->query("SELECT * FROM posts WHERE job_id = ? ORDER BY date_published DESC", [$jobId]);

        if (empty($posts)) {
            echo "\nNo posts found for this job.\n";
        } else {
            echo "\nPosts grabbed:\n";
            foreach ($posts as $post) {
                $futureStatus = isset($post['is_future']) && $post['is_future'] ? 'FUTURE' : 'CURRENT';
                echo "- ID: {$post['id']}, Title: " . htmlspecialchars($post['title']) . ", Date: {$post['date_published']} ($futureStatus)\n";
            }
        }

        echo '</pre>
            </div>
        </div>';
    }

    echo '<a href="web_run_job.php" class="btn btn-secondary">Back to Jobs</a>';
}

// Include tool footer
include_once 'includes/tool_footer.php';
?>
