<?php
/**
 * Enhanced Sitemap Content Grabber
 *
 * This class handles grabbing content from sites using their sitemap with improved reliability.
 */
class EnhancedSitemapGrabber extends ContentGrabber {
    /**
     * Grab content from a sitemap
     *
     * @param int $limit Maximum number of posts to grab
     * @return array Grabbed content
     */
    public function grab($limit = 10) {
        $posts = [];
        $total = 0;
        $newPostsCount = 0;
        $updatedPostsCount = 0;
        $skippedPostsCount = 0;
        $startTime = microtime(true);
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Starting grab with limit $limit");
        }

        // Get job ID if available
        $jobId = isset($this->options['job_id']) ? $this->options['job_id'] : null;

        // Get the last run date for this job
        $lastRunDate = null;
        if ($jobId) {
            $jobInfo = $this->db->query("SELECT last_run FROM jobs WHERE id = ?", [$jobId]);
            if (!empty($jobInfo) && isset($jobInfo[0]['last_run'])) {
                $lastRunDate = $jobInfo[0]['last_run'];
                if ($debug) {
                    error_log("EnhancedSitemapGrabber: Last run date for job $jobId: $lastRunDate");
                }
            }
        }

        // Get existing posts for this job to avoid duplicates
        $existingPosts = [];
        if ($jobId) {
            $existingPostsData = $this->db->query("SELECT external_id FROM posts WHERE job_id = ?", [$jobId]);
            if (!empty($existingPostsData)) {
                foreach ($existingPostsData as $post) {
                    $existingPosts[$post['external_id']] = true;
                }
                if ($debug) {
                    error_log("EnhancedSitemapGrabber: Found " . count($existingPosts) . " existing posts for job $jobId");
                }
            }
        }

        // Try different approaches for maximum compatibility
        $approaches = [
            'direct' => $this->url,
            'robots' => $this->getRobotsUrl(),
            'common' => $this->getCommonSitemapUrls()
        ];

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Using multiple approaches for maximum compatibility");
        }

        $allUrls = [];

        foreach ($approaches as $approach => $urls) {
            if (!is_array($urls)) {
                $urls = [$urls];
            }

            foreach ($urls as $url) {
                if ($debug) {
                    error_log("EnhancedSitemapGrabber: Trying approach: $approach with URL: $url");
                }

                $extractedUrls = $this->extractUrlsFromSitemap($url);

                if (!empty($extractedUrls)) {
                    if ($debug) {
                        error_log("EnhancedSitemapGrabber: Found " . count($extractedUrls) . " URLs with approach: $approach");
                    }

                    $allUrls = array_merge($allUrls, $extractedUrls);

                    // If we have enough URLs, no need to try other approaches
                    if (count($allUrls) >= $limit) {
                        break 2;
                    }
                }
            }
        }

        // Remove duplicate URLs
        $uniqueUrls = [];
        foreach ($allUrls as $url) {
            $uniqueUrls[$url['loc']] = $url;
        }
        $allUrls = array_values($uniqueUrls);

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Found " . count($allUrls) . " unique URLs");
        }

        // Apply date filter if specified
        if (!empty($this->options['after_date'])) {
            $afterTimestamp = strtotime($this->options['after_date']);
            $allUrls = array_filter($allUrls, function($url) use ($afterTimestamp) {
                return $url['lastmod'] >= $afterTimestamp;
            });

            if ($debug) {
                error_log("EnhancedSitemapGrabber: After date filter applied, " . count($allUrls) . " URLs remaining");
            }
        }

        // Apply date range filter if specified
        if (!empty($this->options['before_date'])) {
            $beforeTimestamp = strtotime($this->options['before_date']);
            $allUrls = array_filter($allUrls, function($url) use ($beforeTimestamp) {
                return $url['lastmod'] <= $beforeTimestamp;
            });

            if ($debug) {
                error_log("EnhancedSitemapGrabber: Before date filter applied, " . count($allUrls) . " URLs remaining");
            }
        }

        // Sort by date (newest first)
        usort($allUrls, function($a, $b) {
            return $b['lastmod'] - $a['lastmod'];
        });

        // Limit the number of URLs to process
        $allUrls = array_slice($allUrls, 0, $limit);

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Processing " . count($allUrls) . " URLs");
        }

        // Process each URL
        foreach ($allUrls as $url) {
            try {
                // Generate a unique ID for this URL
                $urlId = md5($url['loc']);

                // Skip if we already have this URL and it hasn't been modified
                if (isset($existingPosts[$urlId])) {
                    // Check if the URL has been modified since last run
                    if ($lastRunDate && isset($url['lastmod']) && $url['lastmod'] > strtotime($lastRunDate)) {
                        if ($debug) {
                            error_log("EnhancedSitemapGrabber: URL {$url['loc']} has been updated since last run, processing");
                        }
                        // Process the updated URL
                        $post = $this->processUrl($url['loc']);
                        if ($post) {
                            $post['is_update'] = true;
                            $post['external_id'] = $urlId; // Store the URL ID for future reference
                            $posts[] = $post;
                            $total++;
                            $updatedPostsCount++;

                            if ($debug) {
                                error_log("EnhancedSitemapGrabber: Processed updated URL: {$url['loc']}");
                            }

                            if ($total >= $limit) {
                                break;
                            }
                        }
                    } else {
                        // Skip this URL as we already have it and it hasn't been updated
                        $skippedPostsCount++;
                        if ($debug) {
                            error_log("EnhancedSitemapGrabber: Skipping existing URL: {$url['loc']}");
                        }
                        continue;
                    }
                } else {
                    // This is a new URL, process it
                    $post = $this->processUrl($url['loc']);
                    if ($post) {
                        $post['is_update'] = false;
                        $post['external_id'] = $urlId; // Store the URL ID for future reference
                        $posts[] = $post;
                        $total++;
                        $newPostsCount++;

                        if ($debug) {
                            error_log("EnhancedSitemapGrabber: Processed new URL: {$url['loc']}");
                        }

                        if ($total >= $limit) {
                            break;
                        }
                    }
                }
            } catch (Exception $e) {
                if ($debug) {
                    error_log("EnhancedSitemapGrabber: Error processing URL {$url['loc']}: " . $e->getMessage());
                }
                continue;
            }
        }

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Grabbed " . count($posts) . " posts");
        }

        // Calculate execution time
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);

        // Prepare statistics
        $stats = [
            'total' => count($posts),
            'new' => $newPostsCount,
            'updated' => $updatedPostsCount,
            'skipped' => $skippedPostsCount,
            'execution_time' => $executionTime,
            'approaches_tried' => count($approaches)
        ];

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Grab completed in $executionTime seconds");
            error_log("EnhancedSitemapGrabber: Stats - Total: {$stats['total']}, New: {$stats['new']}, Updated: {$stats['updated']}, Skipped: {$stats['skipped']}");
        }

        return [
            'posts' => $posts,
            'stats' => $stats
        ];
    }

    /**
     * Get the robots.txt URL for the site
     *
     * @return string URL of the robots.txt file
     */
    protected function getRobotsUrl() {
        $urlParts = parse_url($this->url);
        $baseUrl = $urlParts['scheme'] . '://' . $urlParts['host'];
        return $baseUrl . '/robots.txt';
    }

    /**
     * Get common sitemap URLs for the site
     *
     * @return array Array of common sitemap URLs
     */
    protected function getCommonSitemapUrls() {
        $urlParts = parse_url($this->url);
        $baseUrl = $urlParts['scheme'] . '://' . $urlParts['host'];

        return [
            $baseUrl . '/sitemap.xml',
            $baseUrl . '/sitemap_index.xml',
            $baseUrl . '/sitemap-index.xml',
            $baseUrl . '/sitemapindex.xml',
            $baseUrl . '/wp-sitemap.xml',
            $baseUrl . '/sitemap/sitemap.xml'
        ];
    }

    /**
     * Extract URLs from a sitemap
     *
     * @param string $sitemapUrl URL of the sitemap
     * @return array Array of URLs with metadata
     */
    protected function extractUrlsFromSitemap($sitemapUrl) {
        $urls = [];
        $debug = isset($this->options['debug']) && $this->options['debug'];

        // Check if it's a robots.txt file
        if (strpos($sitemapUrl, 'robots.txt') !== false) {
            return $this->extractUrlsFromRobots($sitemapUrl);
        }

        // Fetch the sitemap
        $response = $this->directFetch($sitemapUrl);

        if (!$response) {
            if ($debug) {
                error_log("EnhancedSitemapGrabber: Failed to fetch sitemap: $sitemapUrl");
            }
            return $urls;
        }

        // Check if it's a gzipped file
        if (substr($response, 0, 2) === "\x1f\x8b") {
            $response = gzdecode($response);

            if ($response === false) {
                if ($debug) {
                    error_log("EnhancedSitemapGrabber: Failed to decompress gzipped sitemap: $sitemapUrl");
                }
                return $urls;
            }
        }

        // Load the XML
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($response);
        $errors = libxml_get_errors();
        libxml_clear_errors();

        if ($xml === false || count($errors) > 0) {
            if ($debug) {
                error_log("EnhancedSitemapGrabber: Failed to parse XML from sitemap: $sitemapUrl");
                foreach ($errors as $error) {
                    error_log("EnhancedSitemapGrabber: XML Error: " . $error->message);
                }
            }
            return $urls;
        }

        // Register namespaces
        $namespaces = $xml->getNamespaces(true);

        // Check if it's a sitemap index
        if ($xml->getName() === 'sitemapindex') {
            if ($debug) {
                error_log("EnhancedSitemapGrabber: Found sitemap index: $sitemapUrl");
            }

            foreach ($xml->sitemap as $sitemap) {
                $sitemapLoc = (string)$sitemap->loc;

                if ($debug) {
                    error_log("EnhancedSitemapGrabber: Found child sitemap: $sitemapLoc");
                }

                $childUrls = $this->extractUrlsFromSitemap($sitemapLoc);
                $urls = array_merge($urls, $childUrls);
            }
        }
        // Check if it's a urlset
        elseif ($xml->getName() === 'urlset') {
            if ($debug) {
                error_log("EnhancedSitemapGrabber: Found urlset: $sitemapUrl");
            }

            foreach ($xml->url as $url) {
                $loc = (string)$url->loc;
                $lastmod = isset($url->lastmod) ? strtotime((string)$url->lastmod) : time();

                $urls[] = [
                    'loc' => $loc,
                    'lastmod' => $lastmod
                ];
            }

            if ($debug) {
                error_log("EnhancedSitemapGrabber: Extracted " . count($urls) . " URLs from urlset");
            }
        }

        return $urls;
    }

    /**
     * Extract sitemap URLs from robots.txt
     *
     * @param string $robotsUrl URL of the robots.txt file
     * @return array Array of URLs with metadata
     */
    protected function extractUrlsFromRobots($robotsUrl) {
        $urls = [];
        $debug = isset($this->options['debug']) && $this->options['debug'];

        // Fetch the robots.txt file
        $response = $this->directFetch($robotsUrl);

        if (!$response) {
            if ($debug) {
                error_log("EnhancedSitemapGrabber: Failed to fetch robots.txt: $robotsUrl");
            }
            return $urls;
        }

        // Extract sitemap URLs
        preg_match_all('/Sitemap:\s*([^\s]+)/i', $response, $matches);

        if (isset($matches[1]) && !empty($matches[1])) {
            if ($debug) {
                error_log("EnhancedSitemapGrabber: Found " . count($matches[1]) . " sitemaps in robots.txt");
            }

            foreach ($matches[1] as $sitemapUrl) {
                $sitemapUrls = $this->extractUrlsFromSitemap($sitemapUrl);
                $urls = array_merge($urls, $sitemapUrls);
            }
        }

        return $urls;
    }

    /**
     * Direct fetch using cURL
     *
     * @param string $url URL to fetch
     * @return string|bool Response or false on failure
     */
    protected function directFetch($url) {
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Direct fetching URL: $url");
        }

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // Increase timeout to 60 seconds
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HEADER, false);

        // Set user agent
        $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

        if ($debug) {
            error_log("EnhancedSitemapGrabber: HTTP Code: $httpCode, Content-Type: $contentType, Response length: " . strlen($response));
        }

        // Check for errors
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            if ($debug) {
                error_log("EnhancedSitemapGrabber: cURL error: $error");
            }
            curl_close($ch);
            return false;
        }

        curl_close($ch);

        // Check if response is successful
        if ($httpCode < 200 || $httpCode >= 300) {
            if ($debug) {
                error_log("EnhancedSitemapGrabber: HTTP error: $httpCode");
            }
            return false;
        }

        return $response;
    }

    /**
     * Process a URL to extract content
     *
     * @param string $url URL to process
     * @return array|bool Processed post data or false on failure
     */
    protected function processUrl($url) {
        // Use the existing implementation from SitemapGrabber
        $response = $this->directFetch($url);

        if (!$response) {
            return false;
        }

        // Parse the HTML
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($response, 'HTML-ENTITIES', 'UTF-8'));
        $xpath = new DOMXPath($dom);

        // Extract post ID or create a hash
        $postId = md5($url);

        // Extract post slug from URL
        $urlParts = parse_url($url);
        $path = $urlParts['path'];
        $pathParts = explode('/', trim($path, '/'));
        $slug = end($pathParts);

        // If slug contains extension, remove it
        $slug = preg_replace('/\.[^.]+$/', '', $slug);

        // Create a unique directory for this post
        $postDir = IMAGES_DIR . '/' . $slug . '_' . $postId;

        // Extract title
        $title = '';
        $titleNode = $xpath->query('//title')->item(0);
        if ($titleNode) {
            $title = $titleNode->nodeValue;
        }

        // Try to get a better title from h1
        $h1Node = $xpath->query('//h1')->item(0);
        if ($h1Node) {
            $title = $h1Node->nodeValue;
        }

        // Extract content
        $content = '';
        $contentNode = $xpath->query('//article | //main | //div[@class="content"] | //div[@id="content"]')->item(0);
        if ($contentNode) {
            $content = $dom->saveHTML($contentNode);
        } else {
            // Fallback to body content
            $bodyNode = $xpath->query('//body')->item(0);
            if ($bodyNode) {
                $content = $dom->saveHTML($bodyNode);
            }
        }

        // Extract date
        $date = date('Y-m-d H:i:s');
        $dateNodes = $xpath->query('//time | //meta[@property="article:published_time"] | //span[contains(@class, "date")]');
        if ($dateNodes->length > 0) {
            $dateNode = $dateNodes->item(0);
            if ($dateNode->nodeName === 'time' && $dateNode->hasAttribute('datetime')) {
                $date = $dateNode->getAttribute('datetime');
            } elseif ($dateNode->nodeName === 'meta') {
                $date = $dateNode->getAttribute('content');
            } else {
                $date = $dateNode->nodeValue;
            }

            // Try to parse the date
            $timestamp = strtotime($date);
            if ($timestamp) {
                $date = date('Y-m-d H:i:s', $timestamp);
            }
        }

        // Extract excerpt
        $excerpt = '';
        $metaDescription = $xpath->query('//meta[@name="description"]')->item(0);
        if ($metaDescription) {
            $excerpt = $metaDescription->getAttribute('content');
        }

        // Initialize post data
        $post = [
            'id' => $postId,
            'title' => html_entity_decode($title),
            'content' => $content,
            'excerpt' => $excerpt,
            'date' => $date,
            'modified' => $date,
            'slug' => $slug,
            'link' => $url,
            'categories' => [],
            'tags' => [],
            'images' => [],
            'featured_image' => null
        ];

        // Extract categories and tags
        $metaKeywords = $xpath->query('//meta[@name="keywords"]')->item(0);
        if ($metaKeywords) {
            $keywords = explode(',', $metaKeywords->getAttribute('content'));
            foreach ($keywords as $keyword) {
                $keyword = trim($keyword);
                if (!empty($keyword)) {
                    $post['tags'][] = [
                        'id' => md5($keyword),
                        'name' => $keyword,
                        'slug' => strtolower(str_replace(' ', '-', $keyword))
                    ];
                }
            }
        }

        // Extract featured image
        $ogImage = $xpath->query('//meta[@property="og:image"]')->item(0);
        if ($ogImage) {
            $imageUrl = $ogImage->getAttribute('content');
            $localPath = $this->downloadImage($imageUrl, $postDir);
            if ($localPath) {
                $post['featured_image'] = [
                    'url' => $imageUrl,
                    'local_path' => $localPath,
                    'alt' => $post['title']
                ];
            }
        }

        // Extract inline images using the extractInlineImages method
        $post = $this->extractInlineImages($post, $postDir);

        // Update the content with modified image paths
        $post['content'] = $dom->saveHTML();

        // Generate HTML and PDF versions
        $this->generateHtmlVersion($post);
        $this->generatePdfVersion($post);

        return $post;
    }

    /**
     * Extract and download inline images from post content
     *
     * @param array $post Processed post data
     * @param string $postDir Directory to save images
     * @return array Updated post data
     */
    protected function extractInlineImages($post, $postDir) {
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Extracting inline images from post: {$post['id']}");
        }

        // Create a DOM document
        $dom = new DOMDocument();

        // Suppress warnings during HTML loading
        libxml_use_internal_errors(true);

        // Load the HTML content
        $dom->loadHTML(mb_convert_encoding($post['content'], 'HTML-ENTITIES', 'UTF-8'));

        // Restore error handling
        libxml_clear_errors();

        // Create XPath object
        $xpath = new DOMXPath($dom);

        // Find all img tags
        $images = $xpath->query('//img');

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Found " . $images->length . " inline images");
        }

        // Process each image
        foreach ($images as $img) {
            // Get the image URL
            $src = $img->getAttribute('src');

            // Skip data URLs
            if (strpos($src, 'data:') === 0) {
                if ($debug) {
                    error_log("EnhancedSitemapGrabber: Skipping data URL");
                }
                continue;
            }

            // Make sure the URL is absolute
            if (strpos($src, 'http') !== 0) {
                // Parse the post URL
                $postUrlParts = parse_url($post['link']);
                $baseUrl = $postUrlParts['scheme'] . '://' . $postUrlParts['host'];

                // Convert relative URL to absolute
                if (strpos($src, '/') === 0) {
                    // URL starts with /, it's relative to the domain root
                    $src = $baseUrl . $src;
                } else {
                    // URL is relative to the post URL
                    $basePath = isset($postUrlParts['path']) ? dirname($postUrlParts['path']) : '';
                    $src = $baseUrl . $basePath . '/' . $src;
                }

                if ($debug) {
                    error_log("EnhancedSitemapGrabber: Converted relative URL to absolute: $src");
                }
            }

            // Get alt text
            $alt = $img->getAttribute('alt');

            // Download the image
            $localPath = $this->downloadImage($src, $postDir);

            if ($localPath) {
                // Add to images array
                $post['images'][] = [
                    'url' => $src,
                    'local_path' => $localPath,
                    'alt' => $alt
                ];

                // Update the image src in the content to point to the local file
                $relativePath = str_replace(BASE_PATH, BASE_URL, $localPath);
                $img->setAttribute('src', $relativePath);

                if ($debug) {
                    error_log("EnhancedSitemapGrabber: Updated image src to: $relativePath");
                }

                // If no featured image, use the first inline image
                if (empty($post['featured_image']) && count($post['images']) === 1) {
                    $post['featured_image'] = [
                        'url' => $src,
                        'local_path' => $localPath,
                        'alt' => $alt,
                        'caption' => ''
                    ];

                    if ($debug) {
                        error_log("EnhancedSitemapGrabber: Using first inline image as featured image");
                    }
                }
            } else if ($debug) {
                error_log("EnhancedSitemapGrabber: Failed to download inline image: $src");
            }
        }

        // Update the content with the modified image paths
        $post['content'] = $dom->saveHTML();

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Extracted " . count($post['images']) . " inline images");
        }

        return $post;
    }

    /**
     * Download an image
     *
     * @param string $url Image URL
     * @param string $dir Directory to save the image
     * @return string|bool Local path to the image or false on failure
     */
    protected function downloadImage($url, $dir) {
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Downloading image: $url to directory: $dir");
        }

        // Skip invalid URLs
        if (empty($url) || strpos($url, 'http') !== 0) {
            if ($debug) {
                error_log("EnhancedSitemapGrabber: Invalid image URL: $url");
            }
            return false;
        }

        // Create directory if it doesn't exist
        if (!file_exists($dir)) {
            if (!mkdir($dir, 0755, true) && !is_dir($dir)) {
                if ($debug) {
                    error_log("EnhancedSitemapGrabber: Failed to create directory: $dir");
                }
                return false;
            }
        }

        // Get image filename from URL
        $urlPath = parse_url($url, PHP_URL_PATH);
        $filename = basename($urlPath);

        // Clean up filename
        $filename = preg_replace('/[^a-zA-Z0-9\-\_\.]/', '', $filename);

        // If filename is empty or invalid, generate a random one
        if (empty($filename) || strpos($filename, '.') === false) {
            $filename = md5($url) . '.jpg';
        }

        // Local path
        $localPath = $dir . '/' . $filename;

        // Check if file already exists
        if (file_exists($localPath)) {
            if ($debug) {
                error_log("EnhancedSitemapGrabber: Image already exists: $localPath");
            }
            return $localPath;
        }

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        // Set user agent
        $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);

        // Execute the request
        $imageData = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Image download HTTP Code: $httpCode, Content-Type: $contentType, Data length: " . strlen($imageData));
        }

        // Check for errors
        if (curl_errno($ch) || $httpCode < 200 || $httpCode >= 300 || empty($imageData)) {
            $error = curl_error($ch);
            if ($debug) {
                error_log("EnhancedSitemapGrabber: Failed to download image: $url, Error: $error, HTTP Code: $httpCode");
            }
            curl_close($ch);
            return false;
        }

        curl_close($ch);

        // Save image to file
        if (file_put_contents($localPath, $imageData) === false) {
            if ($debug) {
                error_log("EnhancedSitemapGrabber: Failed to save image to: $localPath");
            }
            return false;
        }

        if ($debug) {
            error_log("EnhancedSitemapGrabber: Successfully downloaded image to: $localPath");
        }

        return $localPath;
    }

    /**
     * Generate HTML version of the post
     *
     * @param array $post Processed post data
     * @return string Path to the HTML file
     */
    protected function generateHtmlVersion($post) {
        // Use the existing implementation from SitemapGrabber
        // Create the HTML directory if it doesn't exist
        if (!file_exists(HTML_DIR)) {
            mkdir(HTML_DIR, 0755, true);
        }

        // Create HTML file
        $htmlFile = HTML_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.html';

        // Generate HTML content
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($post['title']) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>' . htmlspecialchars($post['title']) . '</h1>
    <div class="meta">Published on: ' . date('F j, Y', strtotime($post['date'])) . '</div>
    <div class="content">' . $post['content'] . '</div>
</body>
</html>';

        // Save HTML file
        file_put_contents($htmlFile, $html);

        return $htmlFile;
    }

    /**
     * Generate PDF version of the post
     *
     * @param array $post Processed post data
     * @return string Path to the PDF file
     */
    protected function generatePdfVersion($post) {
        // Use the existing implementation from SitemapGrabber
        // Create the PDF directory if it doesn't exist
        if (!file_exists(PDF_DIR)) {
            mkdir(PDF_DIR, 0755, true);
        }

        // PDF file path
        $pdfFile = PDF_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.pdf';

        // Simple PDF generation
        $pdfContent = "PDF version of: " . $post['title'];
        file_put_contents($pdfFile, $pdfContent);

        return $pdfFile;
    }
}
?>
