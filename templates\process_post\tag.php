<div class="row">
    <div class="col-md-6">
        <h4 class="mb-3">Original Tags</h4>
        <div class="card mb-4">
            <div class="card-body">
                <?php
                // Get tags
                $tags = $db->query("
                    SELECT t.name 
                    FROM post_tags pt
                    JOIN tags t ON pt.tag_id = t.id
                    WHERE pt.post_id = ?
                ", [$post['id']]);
                
                if (!empty($tags)):
                ?>
                <div>
                    <?php foreach ($tags as $tag): ?>
                    <span class="badge bg-secondary me-1 mb-1"><?php echo htmlspecialchars($tag['name']); ?></span>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <p class="text-muted mb-0">No tags assigned to this post.</p>
                <?php endif; ?>
            </div>
        </div>
        
        <h4 class="mb-3">Process Tags</h4>
        <div class="card mb-4">
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="process_tags">
                    
                    <p class="text-muted">Generate relevant tags for this post based on its content.</p>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-tags me-1"></i> Generate Tags
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <h4 class="mb-3">Generated Tags</h4>
        
        <?php if ($processedPost && !empty($processedPost['processed_tags'])): ?>
        <div class="card mb-4">
            <div class="card-body">
                <?php
                // Parse tags (comma-separated list)
                $generatedTags = array_map('trim', explode(',', $processedPost['processed_tags']));
                ?>
                
                <div class="<?php echo $isRtl ? 'text-end' : ''; ?>" dir="<?php echo $isRtl ? 'rtl' : 'ltr'; ?>">
                    <?php foreach ($generatedTags as $tag): ?>
                    <span class="badge bg-secondary me-1 mb-1"><?php echo htmlspecialchars($tag); ?></span>
                    <?php endforeach; ?>
                </div>
                
                <div class="mt-3">
                    <p class="text-muted mb-0">Raw response:</p>
                    <pre class="bg-light p-2 mt-2 rounded"><?php echo htmlspecialchars($processedPost['processed_tags']); ?></pre>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            Tags have been generated successfully.
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            No generated tags available. Use the form on the left to generate tags.
        </div>
        <?php endif; ?>
    </div>
</div>
