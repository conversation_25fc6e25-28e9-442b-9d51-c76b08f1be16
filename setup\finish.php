<div class="text-center mb-4">
    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
    <h2>Setup Complete!</h2>
    <p class="lead">Your Content Grabber is now ready to use</p>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">Next Steps</h5>
    </div>
    <div class="card-body">
        <ol class="list-group list-group-numbered">
            <li class="list-group-item d-flex justify-content-between align-items-start">
                <div class="ms-2 me-auto">
                    <div class="fw-bold">Log in to your dashboard</div>
                    Use the credentials you just created to access the system
                </div>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-start">
                <div class="ms-2 me-auto">
                    <div class="fw-bold">Create your first job</div>
                    Set up a WordPress site or sitemap URL to grab content from
                </div>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-start">
                <div class="ms-2 me-auto">
                    <div class="fw-bold">Run the job</div>
                    Start grabbing content and see the results
                </div>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-start">
                <div class="ms-2 me-auto">
                    <div class="fw-bold">Explore the features</div>
                    Check out the HTML and PDF versions of the grabbed content
                </div>
            </li>
        </ol>
    </div>
</div>

<div class="alert alert-success">
    <i class="fas fa-info-circle me-2"></i>
    Click the "Finish Setup" button to go to your dashboard.
</div>

<?php
// Create content directories
if (!file_exists(BASE_PATH . '/content')) {
    mkdir(BASE_PATH . '/content', 0755, true);
}
if (!file_exists(BASE_PATH . '/content/images')) {
    mkdir(BASE_PATH . '/content/images', 0755, true);
}
if (!file_exists(BASE_PATH . '/content/html')) {
    mkdir(BASE_PATH . '/content/html', 0755, true);
}
if (!file_exists(BASE_PATH . '/content/pdf')) {
    mkdir(BASE_PATH . '/content/pdf', 0755, true);
}

// Create .htaccess file to protect the setup directory
$htaccess = "Order deny,allow\nDeny from all";
file_put_contents(__DIR__ . '/.htaccess', $htaccess);
?>
