<?php
/**
 * Debug AutoGPT.net Grabber
 * 
 * This script provides detailed debugging for the AutoGptGrabber class.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Create content directories
create_content_directories();

// Load required files
require_once 'includes/Database.php';
require_once 'includes/ContentGrabber.php';
require_once 'includes/AutoGptGrabber.php';

// Initialize database connection
$db = new Database();

// URL to test
$url = 'https://autogpt.net';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 5;
$disable_embed = isset($_GET['disable_embed']) ? (bool)$_GET['disable_embed'] : false;
$debug = true;
$raw_mode = isset($_GET['raw_mode']) ? (bool)$_GET['raw_mode'] : false;

// HTML header
if (!$raw_mode) {
    echo '<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Debug AutoGPT.net Grabber</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <style>
            pre {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                overflow: auto;
                max-height: 400px;
            }
            .success {
                color: green;
                font-weight: bold;
            }
            .error {
                color: red;
                font-weight: bold;
            }
            .warning {
                color: orange;
                font-weight: bold;
            }
            .info {
                color: blue;
            }
            .code {
                font-family: monospace;
                background-color: #f0f0f0;
                padding: 2px 4px;
                border-radius: 3px;
            }
        </style>
    </head>
    <body>
        <div class="container py-5">
            <h1>Debug AutoGPT.net Grabber</h1>
            <p class="lead">This tool provides detailed debugging for the AutoGptGrabber class.</p>
            
            <form method="get" class="mb-4">
                <div class="mb-3">
                    <label for="limit" class="form-label">Number of posts to grab</label>
                    <input type="number" class="form-control" id="limit" name="limit" value="' . $limit . '" min="1" max="100">
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="disable_embed" name="disable_embed" value="1" ' . ($disable_embed ? 'checked' : '') . '>
                    <label class="form-check-label" for="disable_embed">Disable _embed parameter</label>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="raw_mode" name="raw_mode" value="1" ' . ($raw_mode ? 'checked' : '') . '>
                    <label class="form-check-label" for="raw_mode">Raw mode (no HTML)</label>
                </div>
                <button type="submit" class="btn btn-primary">Debug Grabber</button>
            </form>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Debug Results</h5>
                </div>
                <div class="card-body">
                    <pre>';
}

// Start output buffer for raw mode
if ($raw_mode) {
    ob_start();
}

echo "Debugging AutoGptGrabber\n";
echo "URL: $url\n";
echo "Limit: $limit posts\n";
echo "Disable _embed: " . ($disable_embed ? 'Yes' : 'No') . "\n";
echo "Debug mode: Enabled\n\n";

// Create a custom error handler to capture all errors
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    echo "<span class='error'>PHP ERROR [$errno]: $errstr in $errfile on line $errline</span>\n";
    return true;
});

// Create a subclass of AutoGptGrabber to expose protected methods
class DebugAutoGptGrabber extends AutoGptGrabber {
    public function publicDirectFetch($url) {
        return $this->directFetch($url);
    }
    
    public function publicProcessPost($post) {
        return $this->processPost($post);
    }
}

try {
    // Create grabber with debug mode
    echo "<span class='info'>Creating AutoGptGrabber...</span>\n";
    $options = [
        'debug' => $debug,
        'disable_embed' => $disable_embed
    ];
    
    // Create grabber
    $grabber = new DebugAutoGptGrabber($db, $url, $options);
    
    // Test direct API access
    echo "<span class='info'>Testing direct API access...</span>\n";
    $apiUrl = rtrim($url, '/') . '/wp-json/wp/v2/posts?per_page=1&orderby=date&order=desc';
    if ($disable_embed) {
        $apiUrl .= '&_embed=0';
    } else {
        $apiUrl .= '&_embed=1';
    }
    
    echo "API URL: $apiUrl\n";
    $response = $grabber->publicDirectFetch($apiUrl);
    
    if ($response) {
        echo "<span class='success'>API access successful!</span>\n";
        echo "Response length: " . strlen($response) . " bytes\n";
        
        // Parse response
        $data = json_decode($response, true);
        if (is_array($data) && !empty($data)) {
            echo "<span class='success'>Found " . count($data) . " posts!</span>\n";
            
            // Display post details
            $post = $data[0];
            echo "Post details:\n";
            echo "  ID: " . $post['id'] . "\n";
            echo "  Title: " . $post['title']['rendered'] . "\n";
            echo "  Date: " . $post['date'] . "\n";
            echo "  Status: " . $post['status'] . "\n";
            echo "  Link: " . $post['link'] . "\n\n";
            
            // Check if the post date is in the future
            $postDate = strtotime($post['date']);
            $now = time();
            if ($postDate > $now) {
                echo "<span class='warning'>This post has a future date! (" . date('Y-m-d H:i:s', $postDate) . ")</span>\n";
                echo "Current time: " . date('Y-m-d H:i:s', $now) . "\n";
                echo "Time difference: " . ($postDate - $now) . " seconds (" . round(($postDate - $now) / 86400) . " days)\n\n";
            }
            
            // Test processing a post
            echo "<span class='info'>Testing post processing...</span>\n";
            try {
                $processedPost = $grabber->publicProcessPost($post);
                if ($processedPost) {
                    echo "<span class='success'>Post processed successfully!</span>\n";
                    echo "Processed post ID: " . $processedPost['id'] . "\n";
                    echo "Title: " . $processedPost['title'] . "\n";
                    echo "Date: " . $processedPost['date'] . "\n";
                    echo "Is future: " . ($processedPost['is_future'] ? 'Yes' : 'No') . "\n";
                    echo "Categories: " . count($processedPost['categories']) . "\n";
                    echo "Tags: " . count($processedPost['tags']) . "\n";
                    echo "Images: " . count($processedPost['images']) . "\n\n";
                } else {
                    echo "<span class='error'>Post processing failed!</span>\n\n";
                }
            } catch (Exception $e) {
                echo "<span class='error'>Error processing post: " . $e->getMessage() . "</span>\n\n";
            }
        } else {
            echo "<span class='error'>Failed to parse JSON response!</span>\n";
            echo "Response: " . substr($response, 0, 500) . "...\n\n";
        }
    } else {
        echo "<span class='error'>API access failed!</span>\n\n";
    }
    
    // Grab posts
    echo "<span class='info'>Grabbing $limit posts...</span>\n";
    $startTime = microtime(true);
    $posts = $grabber->grab($limit);
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);
    
    if (empty($posts)) {
        echo "<span class='error'>No posts grabbed!</span>\n";
        echo "Execution time: $executionTime seconds\n\n";
        
        // Check error log
        echo "<span class='info'>Checking error log...</span>\n";
        $errorLog = error_get_last();
        if ($errorLog) {
            echo "Last error: " . $errorLog['message'] . " in " . $errorLog['file'] . " on line " . $errorLog['line'] . "\n\n";
        }
        
        // Suggest fixes
        echo "<span class='info'>Suggestions:</span>\n";
        echo "1. Check if the WordPress API is accessible\n";
        echo "2. Verify that the site has posts\n";
        echo "3. Try disabling the _embed parameter\n";
        echo "4. Check for any PHP errors in the logs\n";
    } else {
        echo "<span class='success'>Successfully grabbed " . count($posts) . " posts in $executionTime seconds!</span>\n\n";
        
        // Display post details
        echo "<strong>Post details:</strong>\n";
        echo "------------\n";
        foreach ($posts as $index => $post) {
            echo "Post #" . ($index + 1) . ":\n";
            echo "  ID: " . $post['id'] . "\n";
            echo "  Title: " . $post['title'] . "\n";
            echo "  Date: " . $post['date'] . "\n";
            echo "  Is future: " . ($post['is_future'] ? 'Yes' : 'No') . "\n";
            echo "  Link: " . $post['link'] . "\n";
            echo "  Content length: " . strlen($post['content']) . " bytes\n";
            echo "  Categories: " . count($post['categories']) . "\n";
            echo "  Tags: " . count($post['tags']) . "\n";
            echo "\n";
        }
    }
} catch (Exception $e) {
    echo "<span class='error'>Error: " . $e->getMessage() . "</span>\n";
    echo "File: " . $e->getFile() . " on line " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Restore error handler
restore_error_handler();

// End output buffer for raw mode
if ($raw_mode) {
    $output = ob_get_clean();
    header('Content-Type: text/plain');
    echo $output;
    exit;
}

// HTML footer
echo '</pre>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="settings.php?page=settings" class="btn btn-secondary">Back to Settings</a>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
?>
