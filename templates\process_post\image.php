<div class="row">
    <div class="col-md-6">
        <h4 class="mb-3">Original Featured Image</h4>
        <div class="card mb-4">
            <div class="card-body text-center">
                <?php if (!empty($post['featured_image'])): ?>
                <img src="<?php echo htmlspecialchars($post['featured_image']); ?>" alt="Featured Image" class="img-fluid rounded">
                <?php else: ?>
                <div class="bg-light rounded p-4">
                    <i class="fas fa-image fa-3x text-muted"></i>
                    <p class="mt-2 mb-0 text-muted">No featured image</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <h4 class="mb-3">Image Prompt Generation</h4>
        <div class="card mb-4">
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="generate_image_prompt">
                    
                    <p class="text-muted">Generate a prompt for AI image generation based on the post content.</p>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-magic me-1"></i> Generate Image Prompt
                    </button>
                </form>
            </div>
        </div>
        
        <?php if ($processedPost && !empty($processedPost['image_prompt'])): ?>
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Generated Image Prompt</h5>
            </div>
            <div class="card-body">
                <p><?php echo nl2br(htmlspecialchars($processedPost['image_prompt'])); ?></p>
                
                <form method="post">
                    <input type="hidden" name="action" value="generate_image">
                    
                    <div class="mb-3">
                        <label for="custom_prompt" class="form-label">Custom Prompt (Optional)</label>
                        <textarea class="form-control" id="custom_prompt" name="custom_prompt" rows="3"><?php echo htmlspecialchars($processedPost['image_prompt']); ?></textarea>
                        <div class="form-text">Edit the prompt if needed, or leave as is to use the generated prompt.</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-image me-1"></i> Generate Image
                    </button>
                </form>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <div class="col-md-6">
        <h4 class="mb-3">Generated Image</h4>
        
        <?php if ($processedPost && !empty($processedPost['processed_featured_image'])): ?>
        <div class="card mb-4">
            <div class="card-body text-center">
                <?php
                // Convert local path to URL
                $imageUrl = str_replace(BASE_PATH, BASE_URL, $processedPost['processed_featured_image']);
                ?>
                <img src="<?php echo htmlspecialchars($imageUrl); ?>" alt="Generated Image" class="img-fluid rounded">
            </div>
        </div>
        
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            Image has been generated successfully.
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            No generated image available. First generate an image prompt, then use it to generate an image.
        </div>
        <?php endif; ?>
    </div>
</div>
