<?php
// Check if ai_providers table exists
try {
    $tableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");

    if (!empty($tableExists)) {
        // Get providers
        $providers = $db->query("SELECT * FROM ai_providers ORDER BY name");

        // Check if ai_api_keys table exists
        $apiKeysTableExists = $db->query("SHOW TABLES LIKE 'ai_api_keys'");

        if (!empty($apiKeysTableExists)) {
            // Get API keys
            try {
                // First, get all API keys
                $apiKeys = $db->query("SELECT * FROM ai_api_keys ORDER BY name");

                // If we have API keys, get the provider names
                if (!empty($apiKeys)) {
                    foreach ($apiKeys as &$key) {
                        // Get provider name
                        $provider = $db->getRow("SELECT name FROM ai_providers WHERE id = ?", [$key['provider_id']]);
                        $key['provider_name'] = $provider ? $provider['name'] : 'Unknown Provider';
                    }
                    unset($key); // Break the reference
                }
            } catch (Exception $e) {
                error_log("Error fetching API keys: " . $e->getMessage());
                $apiKeys = [];
            }
        } else {
            $apiKeys = [];
        }
    } else {
        $providers = [];
        $apiKeys = [];
    }
} catch (Exception $e) {
    // Tables don't exist
    $providers = [];
    $apiKeys = [];
}

// Handle edit action
if ($action === 'edit' && $id > 0) {
    $apiKey = $db->getRow("SELECT * FROM ai_api_keys WHERE id = ?", [$id]);
    if (!$apiKey) {
        echo '<div class="alert alert-danger">API key not found.</div>';
        $action = '';
    } else {
        // Get provider name
        $provider = $db->getRow("SELECT name FROM ai_providers WHERE id = ?", [$apiKey['provider_id']]);
        $apiKey['provider_name'] = $provider ? $provider['name'] : 'Unknown Provider';
    }
}
?>

<div class="row">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo $action === 'edit' ? 'Edit API Key' : 'Add API Key'; ?></h5>
                <?php if ($action === 'edit'): ?>
                <span class="badge bg-primary">
                    <i class="fas fa-robot me-1"></i> <?php echo htmlspecialchars($apiKey['provider_name'] ?? 'NOVITA AI'); ?>
                </span>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION['api_key_added']) && $_SESSION['api_key_added']): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-1"></i> API key added successfully!
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php
                    unset($_SESSION['api_key_added']);
                endif;
                ?>
                <form method="post">
                    <input type="hidden" name="action" value="<?php echo $action === 'edit' ? 'update_api_key' : 'add_api_key'; ?>">
                    <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="key_id" value="<?php echo $apiKey['id']; ?>">
                    <?php endif; ?>

                    <div class="mb-3">
                        <label for="provider_id" class="form-label">
                            <i class="fas fa-robot text-primary me-1"></i> AI Provider
                        </label>
                        <?php if ($action === 'edit'): ?>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-robot"></i>
                            </span>
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($apiKey['provider_name'] ?? 'NOVITA AI'); ?>" disabled>
                            <input type="hidden" name="provider_id" value="<?php echo $apiKey['provider_id']; ?>">
                        </div>
                        <?php else: ?>
                        <select class="form-select" id="provider_id" name="provider_id" required>
                            <option value="">Select Provider</option>
                            <?php foreach ($providers as $provider): ?>
                            <option value="<?php echo $provider['id']; ?>">
                                <?php echo htmlspecialchars($provider['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Select the AI provider for this API key</div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-tag text-primary me-1"></i> Key Name
                        </label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo $action === 'edit' ? htmlspecialchars($apiKey['name']) : ''; ?>" placeholder="e.g., Production Key, Test Key" required>
                        <div class="form-text">A descriptive name to identify this API key</div>
                    </div>

                    <div class="mb-3">
                        <label for="api_key" class="form-label">
                            <i class="fas fa-key text-primary me-1"></i> API Key
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="api_key" name="api_key" value="" placeholder="Enter your API key" <?php echo $action === 'edit' ? '' : 'required'; ?>>
                            <button class="btn btn-outline-secondary" type="button" onclick="toggleApiKeyVisibility()">
                                <i class="fas fa-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                        <div class="form-text">
                            <?php echo $action === 'edit' ? 'Leave blank to keep the current API key' : 'Enter the API key provided by the AI service'; ?>
                            <a href="https://novita.ai/api-access" target="_blank" class="ms-1">
                                <i class="fas fa-external-link-alt"></i> Get NOVITA API key
                            </a>
                        </div>
                    </div>

                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-cog text-primary me-1"></i> Key Settings
                            </h6>

                            <div class="form-check form-switch mb-2">
                                <input type="checkbox" class="form-check-input" id="is_default" name="is_default" <?php echo ($action === 'edit' && $apiKey['is_default']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_default">Set as default key</label>
                                <div class="form-text">This key will be used when no specific key is selected</div>
                            </div>

                            <?php if ($action === 'edit'): ?>
                            <div class="form-check form-switch">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $apiKey['is_active'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">Active</label>
                                <div class="form-text">Inactive keys will not be used for API calls</div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <script>
                    function toggleApiKeyVisibility() {
                        const apiKeyInput = document.getElementById('api_key');
                        const toggleIcon = document.getElementById('toggleIcon');

                        if (apiKeyInput.type === 'password') {
                            apiKeyInput.type = 'text';
                            toggleIcon.classList.remove('fa-eye');
                            toggleIcon.classList.add('fa-eye-slash');
                        } else {
                            apiKeyInput.type = 'password';
                            toggleIcon.classList.remove('fa-eye-slash');
                            toggleIcon.classList.add('fa-eye');
                        }
                    }
                    </script>

                    <div class="d-flex justify-content-between">
                        <?php if (empty($providers) && $action !== 'edit'): ?>
                        <button type="button" class="btn btn-primary" disabled>
                            <i class="fas fa-save me-1"></i> Add API Key
                        </button>
                        <small class="text-danger">No providers available. Please initialize the AI database first.</small>
                        <?php else: ?>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> <?php echo $action === 'edit' ? 'Update' : 'Add'; ?> API Key
                        </button>

                        <?php if ($action === 'edit'): ?>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteKeyModal">
                            <i class="fas fa-trash-alt me-1"></i> Delete
                        </button>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">API Keys</h5>

                <div>
                    <?php if ($action === 'edit'): ?>
                    <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=api_keys" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-plus me-1"></i> Add New
                    </a>
                    <?php endif; ?>
                    <a href="<?php echo BASE_URL; ?>/debug_api_keys.php" class="btn btn-sm btn-outline-info ms-2">
                        <i class="fas fa-bug me-1"></i> Debug
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($apiKeys)): ?>
                <div class="p-4 text-center">
                    <?php if (empty($providers)): ?>
                    <div class="alert alert-warning">
                        <p><i class="fas fa-exclamation-triangle me-2"></i> No AI providers found in the database.</p>
                        <a href="<?php echo BASE_URL; ?>/update_ai_tables.php" class="btn btn-primary mt-2">
                            <i class="fas fa-sync-alt me-1"></i> Initialize AI Database
                        </a>
                    </div>
                    <?php else: ?>
                    <p class="text-muted mb-0">No API keys found. Add your first API key to start using AI features.</p>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th>Provider</th>
                                <th>Name</th>
                                <th>Usage</th>
                                <th>Last Used</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($apiKeys as $key): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-box bg-primary bg-opacity-10 text-primary rounded p-2 me-2">
                                            <i class="fas fa-robot"></i>
                                        </div>
                                        <span><?php echo htmlspecialchars($key['provider_name']); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($key['name']); ?></strong>
                                        <?php if ($key['is_default']): ?>
                                        <span class="badge bg-primary ms-1">Default</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="small text-muted">API Key: •••••••••••••••••</div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-box bg-info bg-opacity-10 text-info rounded p-1 me-2">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <span><?php echo number_format($key['usage_count']); ?> calls</span>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($key['last_used']): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-box bg-secondary bg-opacity-10 text-secondary rounded p-1 me-2">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <span><?php echo date('Y-m-d H:i', strtotime($key['last_used'])); ?></span>
                                    </div>
                                    <?php else: ?>
                                    <span class="text-muted">Never used</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($key['is_active']): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=api_keys&action=edit&id=<?php echo $key['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <style>
                .icon-box {
                    width: 32px;
                    height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                </style>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($action === 'edit'): ?>
<!-- Delete API Key Modal -->
<div class="modal fade" id="deleteKeyModal" tabindex="-1" aria-labelledby="deleteKeyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteKeyModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the API key "<?php echo htmlspecialchars($apiKey['name']); ?>"?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="action" value="delete_api_key">
                    <input type="hidden" name="key_id" value="<?php echo $apiKey['id']; ?>">
                    <button type="submit" class="btn btn-danger">Delete API Key</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
