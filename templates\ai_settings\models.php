<?php
// Check if ai_providers table exists
try {
    $tableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");

    if (!empty($tableExists)) {
        // Get providers
        $providers = $db->query("SELECT * FROM ai_providers ORDER BY name");

        // Check if ai_models table exists
        $modelsTableExists = $db->query("SHOW TABLES LIKE 'ai_models'");

        if (!empty($modelsTableExists)) {
            // Get models
            $models = $db->query("
                SELECT m.*, p.name as provider_name
                FROM ai_models m
                JOIN ai_providers p ON m.provider_id = p.id
                ORDER BY p.name, m.name
            ");
        } else {
            $models = [];
        }
    } else {
        $providers = [];
        $models = [];
    }
} catch (Exception $e) {
    // Tables don't exist
    $providers = [];
    $models = [];
}

// Handle edit action
if ($action === 'edit' && $id > 0) {
    $model = $db->getRow("SELECT * FROM ai_models WHERE id = ?", [$id]);
    if (!$model) {
        echo '<div class="alert alert-danger">Model not found.</div>';
        $action = '';
    } else {
        $model['capabilities'] = json_decode($model['capabilities'], true) ?: [];
    }
}
?>

<div class="row">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0"><?php echo $action === 'edit' ? 'Edit Model' : 'Add Model'; ?></h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?php echo $action === 'edit' ? 'update_model' : 'add_model'; ?>">
                    <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="model_id" value="<?php echo $model['id']; ?>">
                    <?php endif; ?>

                    <div class="mb-3">
                        <label for="provider_id" class="form-label">
                            <i class="fas fa-robot text-primary me-1"></i> AI Provider
                        </label>
                        <?php if ($action === 'edit'): ?>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-robot"></i>
                            </span>
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($model['provider_name'] ?? 'NOVITA AI'); ?>" disabled>
                            <input type="hidden" name="provider_id" value="<?php echo $model['provider_id']; ?>">
                        </div>
                        <?php else: ?>
                        <select class="form-select" id="provider_id" name="provider_id" required>
                            <option value="">Select Provider</option>
                            <?php foreach ($providers as $provider): ?>
                            <option value="<?php echo $provider['id']; ?>">
                                <?php echo htmlspecialchars($provider['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Select the AI provider for this model</div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-tag text-primary me-1"></i> Display Name
                        </label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo $action === 'edit' ? htmlspecialchars($model['name']) : ''; ?>" placeholder="e.g., NOVITA GPT-4, NOVITA DALL-E 3" required>
                        <div class="form-text">A user-friendly name for this model</div>
                    </div>

                    <div class="mb-3">
                        <label for="model_identifier" class="form-label">
                            <i class="fas fa-code text-primary me-1"></i> Model Identifier
                        </label>
                        <input type="text" class="form-control" id="model_identifier" name="model_identifier" value="<?php echo $action === 'edit' ? htmlspecialchars($model['model_id']) : ''; ?>" placeholder="e.g., gpt-4, dall-e-3" required>
                        <div class="form-text">The exact identifier used by the provider's API (case-sensitive)</div>
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">
                            <i class="fas fa-cube text-primary me-1"></i> Model Type
                        </label>
                        <div class="input-group">
                            <select class="form-select" id="type" name="type" required>
                                <option value="text" <?php echo ($action === 'edit' && $model['type'] === 'text') ? 'selected' : ''; ?>>Text Generation</option>
                                <option value="image" <?php echo ($action === 'edit' && $model['type'] === 'image') ? 'selected' : ''; ?>>Image Generation</option>
                                <option value="both" <?php echo ($action === 'edit' && $model['type'] === 'both') ? 'selected' : ''; ?>>Both Text & Image</option>
                            </select>
                            <span class="input-group-text">
                                <i class="fas fa-<?php echo ($action === 'edit' && $model['type'] === 'image') ? 'image' : (($action === 'edit' && $model['type'] === 'both') ? 'layer-group' : 'comment-alt'); ?>"></i>
                            </span>
                        </div>
                        <div class="form-text">Select the type of content this model can generate</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-tools text-primary me-1"></i> Model Capabilities
                        </label>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-2">
                                            <input type="checkbox" class="form-check-input" id="capability_translate" name="capabilities[]" value="translate" <?php echo ($action === 'edit' && in_array('translate', $model['capabilities'])) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="capability_translate">
                                                <i class="fas fa-language text-primary me-1"></i> Translate
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input type="checkbox" class="form-check-input" id="capability_rewrite" name="capabilities[]" value="rewrite" <?php echo ($action === 'edit' && in_array('rewrite', $model['capabilities'])) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="capability_rewrite">
                                                <i class="fas fa-pen text-primary me-1"></i> Rewrite
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input type="checkbox" class="form-check-input" id="capability_summarize" name="capabilities[]" value="summarize" <?php echo ($action === 'edit' && in_array('summarize', $model['capabilities'])) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="capability_summarize">
                                                <i class="fas fa-compress-alt text-primary me-1"></i> Summarize
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-2">
                                            <input type="checkbox" class="form-check-input" id="capability_generate" name="capabilities[]" value="generate" <?php echo ($action === 'edit' && in_array('generate', $model['capabilities'])) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="capability_generate">
                                                <i class="fas fa-magic text-primary me-1"></i> Generate
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input type="checkbox" class="form-check-input" id="capability_optimize" name="capabilities[]" value="optimize" <?php echo ($action === 'edit' && in_array('optimize', $model['capabilities'])) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="capability_optimize">
                                                <i class="fas fa-chart-line text-primary me-1"></i> Optimize
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-text mt-2">Select the capabilities this model supports</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="system_prompt" class="form-label">
                            <i class="fas fa-terminal text-primary me-1"></i> System Prompt
                        </label>
                        <textarea class="form-control" id="system_prompt" name="system_prompt" rows="3" placeholder="You are a helpful assistant that can translate, rewrite, and optimize content."><?php echo $action === 'edit' ? htmlspecialchars($model['system_prompt']) : ''; ?></textarea>
                        <div class="form-text">System instructions to guide the model's behavior (for text models only)</div>
                    </div>

                    <div class="card bg-light mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-sliders-h text-primary me-1"></i> Advanced Model Settings
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="max_tokens" class="form-label">Max Tokens</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="max_tokens" name="max_tokens" value="<?php echo $action === 'edit' ? htmlspecialchars($model['max_tokens']) : '4096'; ?>" placeholder="4096">
                                            <span class="input-group-text">tokens</span>
                                        </div>
                                        <div class="form-text">Maximum output length (for text models)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="temperature" class="form-label">Temperature</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="temperature" name="temperature" step="0.1" min="0" max="2" value="<?php echo $action === 'edit' ? htmlspecialchars($model['temperature']) : '0.7'; ?>">
                                            <span class="input-group-text"><i class="fas fa-thermometer-half"></i></span>
                                        </div>
                                        <div class="form-text">Controls randomness (0-2)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if ($action === 'edit'): ?>
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $model['is_active'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">
                                <i class="fas fa-toggle-on text-primary me-1"></i> Model Active
                            </label>
                            <div class="form-text">Inactive models will not be available for use in workflows</div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="d-flex justify-content-between">
                        <?php if (empty($providers) && $action !== 'edit'): ?>
                        <button type="button" class="btn btn-primary" disabled>
                            <i class="fas fa-save me-1"></i> Add Model
                        </button>
                        <small class="text-danger">No providers available. Please initialize the AI database first.</small>
                        <?php else: ?>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> <?php echo $action === 'edit' ? 'Update' : 'Add'; ?> Model
                        </button>

                        <?php if ($action === 'edit'): ?>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModelModal">
                            <i class="fas fa-trash-alt me-1"></i> Delete
                        </button>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Models</h5>

                <?php if ($action === 'edit'): ?>
                <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=models" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-plus me-1"></i> Add New
                </a>
                <?php endif; ?>
            </div>
            <div class="card-body p-0">
                <?php if (empty($models)): ?>
                <div class="p-4 text-center">
                    <?php if (empty($providers)): ?>
                    <div class="alert alert-warning">
                        <p><i class="fas fa-exclamation-triangle me-2"></i> No AI providers found in the database.</p>
                        <a href="<?php echo BASE_URL; ?>/update_ai_tables.php" class="btn btn-primary mt-2">
                            <i class="fas fa-sync-alt me-1"></i> Initialize AI Database
                        </a>
                    </div>
                    <?php else: ?>
                    <p class="text-muted mb-0">No models found. Add your first model to start using AI features.</p>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th>Provider</th>
                                <th>Model</th>
                                <th>Capabilities</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($models as $m): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-box bg-primary bg-opacity-10 text-primary rounded p-2 me-2">
                                            <i class="fas fa-<?php echo $m['type'] === 'text' ? 'comment-alt' : 'image'; ?>"></i>
                                        </div>
                                        <div>
                                            <strong><?php echo htmlspecialchars($m['provider_name']); ?></strong>
                                            <div class="small text-muted"><?php echo htmlspecialchars($m['model_id']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($m['name']); ?></strong>
                                        <?php if ($m['type'] === 'text'): ?>
                                        <span class="badge bg-primary ms-1">Text</span>
                                        <?php elseif ($m['type'] === 'image'): ?>
                                        <span class="badge bg-success ms-1">Image</span>
                                        <?php else: ?>
                                        <span class="badge bg-info ms-1">Both</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $capabilities = json_decode($m['capabilities'], true) ?: [];
                                    foreach ($capabilities as $capability) {
                                        echo '<span class="badge bg-secondary me-1">' . htmlspecialchars($capability) . '</span>';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php if ($m['is_active']): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=models&action=edit&id=<?php echo $m['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($action === 'edit'): ?>
<!-- Delete Model Modal -->
<div class="modal fade" id="deleteModelModal" tabindex="-1" aria-labelledby="deleteModelModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModelModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the model "<?php echo htmlspecialchars($model['name']); ?>"?</p>
                <p class="text-danger">This action cannot be undone. If this model is used in any workflows, the delete operation will fail.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="action" value="delete_model">
                    <input type="hidden" name="model_id" value="<?php echo $model['id']; ?>">
                    <button type="submit" class="btn btn-danger">Delete Model</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
