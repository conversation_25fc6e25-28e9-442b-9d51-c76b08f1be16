<div class="row">
    <div class="col-md-6">
        <h4 class="mb-3">Original Content</h4>
        <div class="card mb-4">
            <div class="card-body">
                <div class="content-preview">
                    <?php echo $post['content']; ?>
                </div>
            </div>
        </div>
        
        <h4 class="mb-3">Process Content</h4>
        <div class="card mb-4">
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="process_content">
                    
                    <div class="mb-3">
                        <label for="task_type" class="form-label">Task Type</label>
                        <select class="form-select" id="task_type" name="task_type">
                            <option value="translate">Translate</option>
                            <option value="rewrite">Rewrite</option>
                            <option value="translate_rewrite">Translate & Rewrite</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-cog me-1"></i> Process Content
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <h4 class="mb-3">Processed Content</h4>
        
        <?php if ($processedPost && !empty($processedPost['processed_content'])): ?>
        <div class="card mb-4">
            <div class="card-body">
                <div class="content-preview <?php echo $isRtl ? 'text-end' : ''; ?>" dir="<?php echo $isRtl ? 'rtl' : 'ltr'; ?>">
                    <?php echo $processedPost['processed_content']; ?>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            Content has been processed successfully.
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            No processed content available. Use the form on the left to process the content.
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
.content-preview {
    max-height: 500px;
    overflow-y: auto;
}
</style>
