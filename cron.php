<?php
/**
 * <PERSON><PERSON> Job Script
 * 
 * This script is meant to be run by a cron job to handle scheduled tasks.
 * Example cron entry (runs every hour):
 * 0 * * * * php /path/to/content-grabber/cron.php
 */

// Set execution time limit to 0 (no limit)
set_time_limit(0);

// Load configuration
require_once 'config.php';

// Check if the application is installed
if (!is_installed()) {
    echo "Application not installed.\n";
    exit(1);
}

// Create content directories
create_content_directories();

// Load required files
require_once 'includes/Database.php';
require_once 'includes/ContentGrabber.php';
require_once 'includes/JobManager.php';

// Initialize database connection
$db = new Database();

// Initialize job manager
$jobManager = new JobManager($db);

// Get current time
$now = new DateTime();
$currentHour = (int)$now->format('G');
$currentDay = (int)$now->format('j');
$currentDayOfWeek = (int)$now->format('w');
$currentDayOfMonth = (int)$now->format('j');

// Get scheduled jobs
$jobs = $db->query("SELECT * FROM jobs WHERE schedule_type IS NOT NULL AND status != 'running'");

// Process each job
$runJobs = [];
foreach ($jobs as $job) {
    $scheduleType = $job['schedule_type'];
    $shouldRun = false;
    
    // Check if job should run based on schedule
    switch ($scheduleType) {
        case 'hourly':
            // Run every hour
            $shouldRun = true;
            break;
            
        case 'daily':
            // Run once a day at midnight
            $shouldRun = ($currentHour === 0);
            break;
            
        case 'weekly':
            // Run once a week on Sunday at midnight
            $shouldRun = ($currentDayOfWeek === 0 && $currentHour === 0);
            break;
            
        case 'monthly':
            // Run once a month on the 1st at midnight
            $shouldRun = ($currentDayOfMonth === 1 && $currentHour === 0);
            break;
    }
    
    if ($shouldRun) {
        $runJobs[] = $job;
    }
}

// Run jobs
$results = [];
foreach ($runJobs as $job) {
    echo "Running job: {$job['name']} (ID: {$job['id']})\n";
    
    try {
        $result = $jobManager->runJob($job['id']);
        $results[$job['id']] = $result;
        
        echo "Job completed successfully. Grabbed {$result['posts_count']} posts.\n";
    } catch (Exception $e) {
        echo "Job failed: {$e->getMessage()}\n";
        $results[$job['id']] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Log results
$logFile = BASE_PATH . '/logs/cron_' . date('Y-m-d') . '.log';
$logDir = dirname($logFile);

// Create logs directory if it doesn't exist
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}

// Write log
$log = "=== Cron Job Run: " . date('Y-m-d H:i:s') . " ===\n";
$log .= "Jobs processed: " . count($runJobs) . "\n\n";

foreach ($results as $jobId => $result) {
    $job = array_filter($runJobs, function($j) use ($jobId) {
        return $j['id'] == $jobId;
    });
    $job = reset($job);
    
    $log .= "Job: {$job['name']} (ID: {$job['id']})\n";
    $log .= "Status: " . ($result['success'] ? 'Success' : 'Failed') . "\n";
    
    if ($result['success']) {
        $log .= "Posts grabbed: {$result['posts_count']}\n";
    } else {
        $log .= "Error: {$result['error']}\n";
    }
    
    $log .= "\n";
}

file_put_contents($logFile, $log, FILE_APPEND);

echo "Cron job completed.\n";
exit(0);
