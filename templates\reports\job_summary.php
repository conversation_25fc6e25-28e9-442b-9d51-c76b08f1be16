<div class="report-header">
    <h3>Job Summary Report</h3>
    <p class="text-muted">Generated on <?php echo date('F j, Y, g:i a'); ?></p>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-light">
            <div class="card-body text-center py-3">
                <h5 class="card-title">Total Jobs</h5>
                <p class="card-text h3"><?php echo $reportData['total_jobs']; ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-light">
            <div class="card-body text-center py-3">
                <h5 class="card-title">Active Jobs</h5>
                <p class="card-text h3"><?php echo $reportData['active_jobs']; ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-light">
            <div class="card-body text-center py-3">
                <h5 class="card-title">Failed Jobs</h5>
                <p class="card-text h3"><?php echo $reportData['failed_jobs']; ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-light">
            <div class="card-body text-center py-3">
                <h5 class="card-title">Total Posts</h5>
                <p class="card-text h3"><?php echo $reportData['total_posts']; ?></p>
            </div>
        </div>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Job Name</th>
                <th>Type</th>
                <th>Status</th>
                <th>Posts</th>
                <th>Images</th>
                <th>Success Rate</th>
                <th>Last Run</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($reportData['jobs'] as $job): ?>
            <tr>
                <td><?php echo htmlspecialchars($job['name']); ?></td>
                <td>
                    <?php if ($job['type'] === 'wordpress'): ?>
                    <span class="badge bg-primary">WordPress</span>
                    <?php else: ?>
                    <span class="badge bg-info">Sitemap</span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php
                    switch ($job['status']) {
                        case 'pending':
                            echo '<span class="badge bg-secondary">Pending</span>';
                            break;
                        case 'running':
                            echo '<span class="badge bg-warning">Running</span>';
                            break;
                        case 'completed':
                            echo '<span class="badge bg-success">Completed</span>';
                            break;
                        case 'failed':
                            echo '<span class="badge bg-danger">Failed</span>';
                            break;
                    }
                    ?>
                </td>
                <td><?php echo $job['post_count']; ?></td>
                <td><?php echo $job['image_count']; ?></td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar <?php echo $job['success_rate'] >= 80 ? 'bg-success' : ($job['success_rate'] >= 50 ? 'bg-warning' : 'bg-danger'); ?>" role="progressbar" style="width: <?php echo $job['success_rate']; ?>%;" aria-valuenow="<?php echo $job['success_rate']; ?>" aria-valuemin="0" aria-valuemax="100">
                            <?php echo $job['success_rate']; ?>%
                        </div>
                    </div>
                </td>
                <td><?php echo $job['last_run'] ? date('M d, Y H:i', strtotime($job['last_run'])) : 'Never'; ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<div class="mt-4">
    <h4>Job Performance Summary</h4>
    <p>This report provides an overview of all jobs in the system, their performance, and success rates. Use this information to identify jobs that may need attention or optimization.</p>
    
    <div class="alert alert-info">
        <strong>Recommendations:</strong>
        <ul class="mb-0">
            <li>Jobs with a success rate below 50% should be reviewed and potentially reconfigured.</li>
            <li>Consider scheduling high-performing jobs more frequently.</li>
            <li>Check failed jobs for common issues like URL changes or access restrictions.</li>
        </ul>
    </div>
</div>
