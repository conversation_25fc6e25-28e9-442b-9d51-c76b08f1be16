<?php
// Set page title
$pageTitle = 'Posts';

// Handle actions and filters
$action = isset($_GET['action']) ? $_GET['action'] : '';
$postId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$jobId = isset($_GET['job_id']) ? (int)$_GET['job_id'] : 0;
$categoryId = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$view = isset($_GET['view']) ? $_GET['view'] : 'grid';

// Get filter data
$jobs = $db->query("SELECT id, name FROM jobs ORDER BY name");
$categories = $db->query("SELECT id, name FROM categories ORDER BY name");

// Build filter URL parameters
$filterParams = [];
if ($jobId) $filterParams[] = 'job_id=' . $jobId;
if ($categoryId) $filterParams[] = 'category_id=' . $categoryId;
if ($search) $filterParams[] = 'search=' . urlencode($search);
if ($dateFrom) $filterParams[] = 'date_from=' . urlencode($dateFrom);
if ($dateTo) $filterParams[] = 'date_to=' . urlencode($dateTo);
$filterQuery = !empty($filterParams) ? '&' . implode('&', $filterParams) : '';

// Set page actions with enhanced filters
$pageActions = '
<div class="d-flex gap-2 flex-wrap">
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
        <i class="fas fa-filter me-1"></i> Advanced Filters
    </button>
    <div class="btn-group">
        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-eye me-1"></i> View
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item ' . ($view === 'grid' ? 'active' : '') . '" href="' . BASE_URL . '/?page=posts' . $filterQuery . '&view=grid">
                <i class="fas fa-th-large me-2"></i> Grid View
            </a></li>
            <li><a class="dropdown-item ' . ($view === 'list' ? 'active' : '') . '" href="' . BASE_URL . '/?page=posts' . $filterQuery . '&view=list">
                <i class="fas fa-list me-2"></i> List View
            </a></li>
        </ul>
    </div>
</div>';

// Handle post deletion
if ($action === 'delete' && $postId > 0 && $_SERVER['REQUEST_METHOD'] === 'POST') {
    // Delete post images
    $db->delete('images', 'post_id = ?', [$postId]);

    // Delete post category relationships
    $db->delete('post_categories', 'post_id = ?', [$postId]);

    // Delete post tag relationships
    $db->delete('post_tags', 'post_id = ?', [$postId]);

    // Delete post
    $db->delete('posts', 'id = ?', [$postId]);

    // Redirect to posts list
    header('Location: ' . BASE_URL . '/?page=posts&success=' . urlencode('Post deleted successfully!'));
    exit;
}

// Display success/error messages
if (isset($_GET['success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($_GET['success']) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

if (isset($_GET['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($_GET['error']) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

// Display appropriate content based on action
switch ($action) {
    case 'view':
        // View post details
        $post = $db->getRow("SELECT p.*, j.name as job_name FROM posts p JOIN jobs j ON p.job_id = j.id WHERE p.id = ?", [$postId]);

        if (!$post) {
            echo '<div class="alert alert-danger">Post not found!</div>';
            echo '<p><a href="' . BASE_URL . '/?page=posts" class="btn btn-primary">Back to Posts</a></p>';
            break;
        }

        // Get post categories
        $categories = $db->query("
            SELECT c.*
            FROM categories c
            JOIN post_categories pc ON c.id = pc.category_id
            WHERE pc.post_id = ?
        ", [$post['id']]);

        // Get post tags
        $tags = $db->query("
            SELECT t.*
            FROM tags t
            JOIN post_tags pt ON t.id = pt.tag_id
            WHERE pt.post_id = ?
        ", [$post['id']]);

        // Get post images
        $images = $db->query("SELECT * FROM images WHERE post_id = ?", [$post['id']]);

        // Parse featured image
        $featuredImage = json_decode($post['featured_image'], true);
        ?>
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Post Details</h5>
                <div>
                    <a href="<?php echo $post['url']; ?>" target="_blank" class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-external-link-alt me-1"></i> Original
                    </a>
                    <?php if ($post['html_file'] && file_exists($post['html_file'])): ?>
                    <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $post['html_file']); ?>" target="_blank" class="btn btn-outline-info btn-sm me-2">
                        <i class="fas fa-code me-1"></i> HTML
                    </a>
                    <?php endif; ?>
                    <?php if ($post['pdf_file'] && file_exists($post['pdf_file'])): ?>
                    <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $post['pdf_file']); ?>" target="_blank" class="btn btn-outline-danger btn-sm me-2">
                        <i class="fas fa-file-pdf me-1"></i> PDF
                    </a>
                    <?php endif; ?>
                    <a href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $post['id']; ?>" class="btn btn-success btn-sm">
                        <i class="fas fa-brain me-1"></i> AI Process
                    </a>
                </div>
            </div>
            <div class="card-body">
                <h1 class="mb-3"><?php echo htmlspecialchars($post['title']); ?></h1>

                <div class="mb-4">
                    <span class="badge bg-primary me-2">
                        <i class="fas fa-calendar-alt me-1"></i> <?php echo date('F j, Y', strtotime($post['date_published'])); ?>
                    </span>
                    <span class="badge bg-secondary me-2">
                        <i class="fas fa-tasks me-1"></i> <?php echo htmlspecialchars($post['job_name']); ?>
                    </span>
                    <?php foreach ($categories as $category): ?>
                    <span class="badge bg-info me-2">
                        <i class="fas fa-folder me-1"></i> <?php echo htmlspecialchars($category['name']); ?>
                    </span>
                    <?php endforeach; ?>
                </div>

                <?php if ($featuredImage): ?>
                <div class="mb-4">
                    <img src="<?php echo str_replace(BASE_PATH, BASE_URL, $featuredImage['local_path']); ?>" alt="<?php echo htmlspecialchars($featuredImage['alt'] ?? $post['title']); ?>" class="img-fluid rounded">
                </div>
                <?php endif; ?>

                <?php if ($post['excerpt']): ?>
                <div class="card mb-4">
                    <div class="card-body bg-light">
                        <h5>Excerpt</h5>
                        <p class="mb-0"><?php echo $post['excerpt']; ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <div class="content mb-4">
                    <?php echo $post['content']; ?>
                </div>

                <?php if (!empty($tags)): ?>
                <div class="mb-4">
                    <h5>Tags</h5>
                    <?php foreach ($tags as $tag): ?>
                    <span class="badge bg-secondary me-1 mb-1"><?php echo htmlspecialchars($tag['name']); ?></span>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($images)): ?>
                <div class="mb-4">
                    <h5>Images (<?php echo count($images); ?>)</h5>
                    <div class="row">
                        <?php foreach ($images as $image): ?>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $image['local_path']); ?>" target="_blank">
                                <img src="<?php echo str_replace(BASE_PATH, BASE_URL, $image['local_path']); ?>" alt="<?php echo htmlspecialchars($image['alt']); ?>" class="img-thumbnail">
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <div class="d-flex justify-content-between mt-4">
                    <a href="<?php echo BASE_URL; ?>/?page=posts" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Posts
                    </a>
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash me-1"></i> Delete Post
                    </button>
                </div>

                <!-- Delete Modal -->
                <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                Are you sure you want to delete the post "<?php echo htmlspecialchars($post['title']); ?>"?
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <form method="post" action="<?php echo BASE_URL; ?>/?page=posts&action=delete&id=<?php echo $post['id']; ?>">
                                    <button type="submit" class="btn btn-danger">Delete</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        break;

    default:
        // Posts list with enhanced filtering
        // Build query
        $sql = "SELECT DISTINCT p.*, j.name as job_name FROM posts p
                JOIN jobs j ON p.job_id = j.id";
        $params = [];
        $whereConditions = [];

        // Add category join if filtering by category
        if ($categoryId > 0) {
            $sql .= " JOIN post_categories pc ON p.id = pc.post_id";
        }

        // Job filter
        if ($jobId > 0) {
            $whereConditions[] = "p.job_id = ?";
            $params[] = $jobId;
        }

        // Category filter
        if ($categoryId > 0) {
            $whereConditions[] = "pc.category_id = ?";
            $params[] = $categoryId;
        }

        // Search filter
        if (!empty($search)) {
            $whereConditions[] = "(p.title LIKE ? OR p.content LIKE ? OR p.excerpt LIKE ?)";
            $searchTerm = '%' . $search . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        // Date range filter
        if (!empty($dateFrom)) {
            $whereConditions[] = "p.date_published >= ?";
            $params[] = $dateFrom;
        }

        if (!empty($dateTo)) {
            $whereConditions[] = "p.date_published <= ?";
            $params[] = $dateTo . ' 23:59:59';
        }

        // Add WHERE clause if we have conditions
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $sql .= " ORDER BY p.date_published DESC";

        // Get posts
        $posts = $db->query($sql, $params);

        // Enhanced header with search, job filter, and view toggle
        echo '<div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3 mb-lg-0">
                <form method="GET" action="' . BASE_URL . '/" class="d-flex gap-2">
                    <input type="hidden" name="page" value="posts">
                    <input type="hidden" name="view" value="' . $view . '">';
                    if ($jobId) echo '<input type="hidden" name="job_id" value="' . $jobId . '">';
                    if ($categoryId) echo '<input type="hidden" name="category_id" value="' . $categoryId . '">';
                    if ($dateFrom) echo '<input type="hidden" name="date_from" value="' . htmlspecialchars($dateFrom) . '">';
                    if ($dateTo) echo '<input type="hidden" name="date_to" value="' . htmlspecialchars($dateTo) . '">';
                    echo '<div class="input-group">
                        <input type="text" class="form-control" name="search" value="' . htmlspecialchars($search) . '" placeholder="Quick search posts...">
                        <button class="btn btn-outline-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            <div class="col-lg-3 col-md-6 mb-3 mb-lg-0">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-rocket me-2"></i>
                        ' . ($jobId ? 'Job: ' . htmlspecialchars($jobs[array_search($jobId, array_column($jobs, 'id'))]['name'] ?? 'Unknown') : 'All Jobs') . '
                    </button>
                    <ul class="dropdown-menu w-100">
                        <li><a class="dropdown-item ' . (!$jobId ? 'active' : '') . '" href="' . BASE_URL . '/?page=posts&view=' . $view . ($categoryId ? '&category_id=' . $categoryId : '') . ($search ? '&search=' . urlencode($search) : '') . ($dateFrom ? '&date_from=' . urlencode($dateFrom) : '') . ($dateTo ? '&date_to=' . urlencode($dateTo) : '') . '">
                            <i class="fas fa-list me-2"></i> All Jobs
                        </a></li>
                        <li><hr class="dropdown-divider"></li>';
                        foreach ($jobs as $job) {
                            echo '<li><a class="dropdown-item ' . ($jobId == $job['id'] ? 'active' : '') . '" href="' . BASE_URL . '/?page=posts&job_id=' . $job['id'] . '&view=' . $view . ($categoryId ? '&category_id=' . $categoryId : '') . ($search ? '&search=' . urlencode($search) : '') . ($dateFrom ? '&date_from=' . urlencode($dateFrom) : '') . ($dateTo ? '&date_to=' . urlencode($dateTo) : '') . '">
                                <i class="fas fa-rocket me-2"></i> ' . htmlspecialchars($job['name']) . '
                            </a></li>';
                        }
                    echo '</ul>
                </div>
            </div>
            <div class="col-lg-5 d-flex justify-content-end align-items-center gap-3">
                <div class="d-flex align-items-center gap-2">
                    <span class="text-muted small">View:</span>
                    <div class="btn-group" role="group">
                        <a href="' . BASE_URL . '/?page=posts' . $filterQuery . '&view=grid" class="btn btn-sm ' . ($view !== 'list' ? 'btn-primary' : 'btn-outline-primary') . '">
                            <i class="fas fa-th-large"></i>
                        </a>
                        <a href="' . BASE_URL . '/?page=posts' . $filterQuery . '&view=list" class="btn btn-sm ' . ($view === 'list' ? 'btn-primary' : 'btn-outline-primary') . '">
                            <i class="fas fa-list"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <span class="badge bg-primary fs-6 px-3 py-2">
                        <i class="fas fa-newspaper me-1"></i>
                        ' . count($posts) . ' Posts
                    </span>
                </div>
            </div>
        </div>';

        // Active filters display
        if (!empty($filterParams)) {
            echo '<div class="mb-3">
                <div class="d-flex align-items-center gap-2 flex-wrap">
                    <span class="text-muted small fw-semibold">Active Filters:</span>';
                    if ($search) echo '<span class="badge bg-primary">Search: "' . htmlspecialchars($search) . '"</span>';
                    if ($jobId) {
                        $jobName = '';
                        foreach ($jobs as $job) {
                            if ($job['id'] == $jobId) {
                                $jobName = $job['name'];
                                break;
                            }
                        }
                        echo '<span class="badge bg-info">Job: ' . htmlspecialchars($jobName) . '</span>';
                    }
                    if ($categoryId) {
                        $categoryName = '';
                        foreach ($categories as $category) {
                            if ($category['id'] == $categoryId) {
                                $categoryName = $category['name'];
                                break;
                            }
                        }
                        echo '<span class="badge bg-success">Category: ' . htmlspecialchars($categoryName) . '</span>';
                    }
                    if ($dateFrom) echo '<span class="badge bg-warning">From: ' . htmlspecialchars($dateFrom) . '</span>';
                    if ($dateTo) echo '<span class="badge bg-warning">To: ' . htmlspecialchars($dateTo) . '</span>';
                    echo '<a href="' . BASE_URL . '/?page=posts&view=' . $view . '" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> Clear All
                    </a>
                </div>
            </div>';
        }

        if (empty($posts)) {
            echo '<div class="alert alert-info">No posts found. Run a job to grab content.</div>';
        } elseif ($view === 'list') {
            // Enhanced List view
            ?>
            <div class="card border-0 shadow-sm table-modern">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th style="width: 50px;"></th>
                                    <th>Title</th>
                                    <th>Job</th>
                                    <th>Date Published</th>
                                    <th>Images</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($posts as $post): ?>
                                <tr>
                                    <td>
                                        <?php
                                        $featuredImage = json_decode($post['featured_image'], true);
                                        $imagePath = $featuredImage ? str_replace(BASE_PATH, BASE_URL, $featuredImage['local_path']) : BASE_URL . '/assets/images/placeholder.jpg';
                                        ?>
                                        <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>" class="text-decoration-none">
                                            <img src="<?php echo $imagePath; ?>" alt="<?php echo htmlspecialchars($post['title']); ?>"
                                                 class="rounded" style="width: 40px; height: 40px; object-fit: cover;">
                                        </a>
                                    </td>
                                    <td>
                                        <div>
                                            <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>"
                                               class="text-decoration-none fw-semibold hover-primary">
                                                <?php echo htmlspecialchars($post['title']); ?>
                                            </a>
                                            <?php if (!empty($post['excerpt'])): ?>
                                            <div class="small text-muted mt-1" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                                                <?php echo strip_tags($post['excerpt']); ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary bg-opacity-10 text-primary">
                                            <i class="fas fa-rocket me-1"></i>
                                            <?php echo htmlspecialchars($post['job_name']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-alt text-muted me-2"></i>
                                            <span><?php echo date('M d, Y', strtotime($post['date_published'])); ?></span>
                                        </div>
                                        <div class="small text-muted">
                                            <?php echo date('H:i', strtotime($post['date_published'])); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-images text-muted me-2"></i>
                                            <span class="badge bg-info bg-opacity-10 text-info">
                                                <?php
                                                $imageCount = $db->getValue("SELECT COUNT(*) FROM images WHERE post_id = ?", [$post['id']]);
                                                echo $imageCount;
                                                ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>"
                                               class="btn btn-sm btn-primary" title="View Post">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $post['id']; ?>"
                                               class="btn btn-sm btn-success" title="AI Process Post">
                                                <i class="fas fa-brain"></i>
                                            </a>
                                            <?php if ($post['html_file'] && file_exists($post['html_file'])): ?>
                                            <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $post['html_file']); ?>"
                                               target="_blank" class="btn btn-sm btn-info" title="View HTML">
                                                <i class="fas fa-code"></i>
                                            </a>
                                            <?php endif; ?>
                                            <?php if ($post['pdf_file'] && file_exists($post['pdf_file'])): ?>
                                            <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $post['pdf_file']); ?>"
                                               target="_blank" class="btn btn-sm btn-danger" title="View PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php
        } else {
            // Enhanced Grid view
            ?>
            <div class="row">
                <?php foreach ($posts as $post): ?>
                <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm hover-lift transition-all">
                        <?php
                        $featuredImage = json_decode($post['featured_image'], true);
                        $imagePath = $featuredImage ? str_replace(BASE_PATH, BASE_URL, $featuredImage['local_path']) : BASE_URL . '/assets/images/placeholder.jpg';
                        ?>
                        <!-- Clickable Image -->
                        <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>" class="text-decoration-none">
                            <div class="position-relative overflow-hidden" style="border-radius: var(--radius-xl) var(--radius-xl) 0 0;">
                                <img src="<?php echo $imagePath; ?>" class="card-img-top transition-all" alt="<?php echo htmlspecialchars($post['title']); ?>" style="height: 220px; object-fit: cover; transition: transform 0.3s ease;">
                                <div class="position-absolute top-0 end-0 m-3">
                                    <span class="badge bg-primary bg-opacity-90">
                                        <i class="fas fa-eye me-1"></i> View
                                    </span>
                                </div>
                                <!-- Hover overlay -->
                                <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-0 transition-all d-flex align-items-center justify-content-center" style="transition: background-color 0.3s ease;">
                                    <i class="fas fa-search-plus text-white opacity-0" style="font-size: 2rem; transition: opacity 0.3s ease;"></i>
                                </div>
                            </div>
                        </a>

                        <div class="card-body d-flex flex-column">
                            <!-- Clickable Title -->
                            <h5 class="card-title mb-3">
                                <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>" class="text-decoration-none text-dark hover-primary">
                                    <?php echo htmlspecialchars($post['title']); ?>
                                </a>
                            </h5>

                            <!-- Post Meta -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-calendar-alt text-muted me-2"></i>
                                    <span class="small text-muted"><?php echo date('M d, Y', strtotime($post['date_published'])); ?></span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-rocket text-muted me-2"></i>
                                    <span class="small text-muted"><?php echo htmlspecialchars($post['job_name']); ?></span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-images text-muted me-2"></i>
                                    <span class="small text-muted">
                                        <?php
                                        $imageCount = $db->getValue("SELECT COUNT(*) FROM images WHERE post_id = ?", [$post['id']]);
                                        echo $imageCount . ' image' . ($imageCount != 1 ? 's' : '');
                                        ?>
                                    </span>
                                </div>
                            </div>

                            <!-- Excerpt if available -->
                            <?php if (!empty($post['excerpt'])): ?>
                            <p class="card-text small text-muted mb-3" style="display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden;">
                                <?php echo strip_tags($post['excerpt']); ?>
                            </p>
                            <?php endif; ?>

                            <!-- Action Buttons -->
                            <div class="mt-auto">
                                <div class="d-flex gap-2">
                                    <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>" class="btn btn-primary btn-sm flex-fill">
                                        <i class="fas fa-eye me-1"></i> View Post
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $post['id']; ?>" class="btn btn-success btn-sm" title="AI Process">
                                        <i class="fas fa-brain"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php
        }

        // Add filter modal
        ?>
        <!-- Advanced Filter Modal -->
        <div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="filterModalLabel">
                            <i class="fas fa-filter me-2"></i> Advanced Filters
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form method="GET" action="<?php echo BASE_URL; ?>/">
                        <input type="hidden" name="page" value="posts">
                        <input type="hidden" name="view" value="<?php echo $view; ?>">

                        <div class="modal-body">
                            <div class="row">
                                <!-- Search -->
                                <div class="col-md-6 mb-3">
                                    <label for="search" class="form-label">Search</label>
                                    <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search in title, content, excerpt...">
                                </div>

                                <!-- Job Filter -->
                                <div class="col-md-6 mb-3">
                                    <label for="job_id" class="form-label">Job</label>
                                    <select class="form-select" id="job_id" name="job_id">
                                        <option value="">All Jobs</option>
                                        <?php foreach ($jobs as $job): ?>
                                        <option value="<?php echo $job['id']; ?>" <?php echo $jobId == $job['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($job['name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Category Filter -->
                                <div class="col-md-6 mb-3">
                                    <label for="category_id" class="form-label">Category</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <option value="">All Categories</option>
                                        <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" <?php echo $categoryId == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Date From -->
                                <div class="col-md-3 mb-3">
                                    <label for="date_from" class="form-label">Date From</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($dateFrom); ?>">
                                </div>

                                <!-- Date To -->
                                <div class="col-md-3 mb-3">
                                    <label for="date_to" class="form-label">Date To</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($dateTo); ?>">
                                </div>
                            </div>

                            <!-- Active Filters Display -->
                            <?php if (!empty($filterParams)): ?>
                            <div class="mt-4">
                                <h6 class="text-muted mb-2">Active Filters:</h6>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php if ($search): ?>
                                    <span class="badge bg-primary">Search: "<?php echo htmlspecialchars($search); ?>"</span>
                                    <?php endif; ?>
                                    <?php if ($jobId): ?>
                                    <span class="badge bg-info">Job: <?php echo htmlspecialchars($jobs[array_search($jobId, array_column($jobs, 'id'))]['name'] ?? 'Unknown'); ?></span>
                                    <?php endif; ?>
                                    <?php if ($categoryId): ?>
                                    <span class="badge bg-success">Category: <?php echo htmlspecialchars($categories[array_search($categoryId, array_column($categories, 'id'))]['name'] ?? 'Unknown'); ?></span>
                                    <?php endif; ?>
                                    <?php if ($dateFrom): ?>
                                    <span class="badge bg-warning">From: <?php echo htmlspecialchars($dateFrom); ?></span>
                                    <?php endif; ?>
                                    <?php if ($dateTo): ?>
                                    <span class="badge bg-warning">To: <?php echo htmlspecialchars($dateTo); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="modal-footer">
                            <a href="<?php echo BASE_URL; ?>/?page=posts&view=<?php echo $view; ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i> Clear Filters
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i> Apply Filters
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php
        break;
}
?>
