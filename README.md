# Content Grabber

A modern PHP script for grabbing content from WordPress sites and sitemaps with comprehensive features including automatic detection, content extraction, image downloading, database storage, and more.

## Features

- **Automatic Detection**: Automatically detects WordPress sites with REST API support
- **Content Extraction**: Grabs titles, excerpts, feature images, categories, dates, tags, and inline images
- **Image Downloading**: Downloads all images to post-specific folders
- **Database Storage**: Stores everything in a well-structured database
- **Setup Wizard**: Simple setup wizard to create database and user
- **Dynamic Paths**: Can be installed anywhere, including subfolders
- **Job Management**: Add manual and automated jobs to grab content
- **Beautiful UI**: Modern, responsive interface for viewing and managing content
- **HTML/PDF Generation**: Generates beautiful HTML and PDF versions of articles
- **Process Dashboard**: Advanced processing features for each post

## Requirements

- PHP 7.4 or higher
- MySQL/MariaDB database
- PDO, cURL, DOM, and JSON PHP extensions
- Composer (optional, for PDF generation)

## Installation

### Manual Installation

1. Download or clone this repository to your web server
2. Navigate to the application URL in your browser
3. Follow the setup wizard to configure the database and create a user
4. Log in with the credentials you created

### Using Composer

```bash
# Clone the repository
git clone https://github.com/yourusername/content-grabber.git

# Navigate to the directory
cd content-grabber

# Install dependencies
composer install

# Set proper permissions
chmod -R 755 .
chmod -R 777 content/
```

## Usage

### Creating Jobs

1. Log in to the application
2. Go to the Jobs page
3. Click "New Job"
4. Enter the WordPress site URL or sitemap URL
5. Configure job settings (posts per run, category filters, date filters, etc.)
6. Save the job

### Running Jobs

- Click "Run Now" on a job to start grabbing content
- The system will automatically detect if it's a WordPress site or sitemap
- Content will be grabbed and stored in the database
- Images will be downloaded to post-specific folders

### Viewing Content

- Go to the Posts page to view grabbed content
- Switch between grid and list views
- Click on a post to view details
- Access HTML and PDF versions of the post

### Processing Content

- Click "Process" on a post to access the processing dashboard
- Edit post title and content
- Regenerate HTML and PDF files
- View and manage images

### API Access

The Content Grabber provides a RESTful API for programmatic access:

1. Generate an API key in the Settings page
2. Use the API key in the `X-API-Key` header for authentication
3. Access the following endpoints:
   - `/api/?endpoint=jobs` - Manage jobs
   - `/api/?endpoint=posts` - Manage posts
   - `/api/?endpoint=grab` - Grab content directly

## Scheduled Jobs

To run scheduled jobs, set up a cron job to run the `cron.php` script:

```bash
# Run every hour
0 * * * * php /path/to/content-grabber/cron.php
```

## Directory Structure

```
/
├── api/                  # API endpoints
├── assets/               # CSS, JS, and image files
├── content/              # Downloaded content
│   ├── images/           # Downloaded images
│   ├── html/             # Generated HTML files
│   └── pdf/              # Generated PDF files
├── includes/             # Core PHP classes
├── logs/                 # Log files
├── setup/                # Setup wizard files
├── templates/            # HTML templates
├── composer.json         # Composer configuration
├── config.php            # Configuration settings
├── cron.php              # Cron job script
├── index.php             # Main entry point
├── login.php             # Login page
├── logout.php            # Logout script
└── README.md             # This file
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Bootstrap for the UI framework
- Font Awesome for the icons
- mPDF for PDF generation
