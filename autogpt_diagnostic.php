<?php
/**
 * AutoGPT.net Diagnostic Tool
 * 
 * This script performs a comprehensive diagnosis of the autogpt.net WordPress API.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoGPT.net Diagnostic Tool</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow: auto;
            max-height: 400px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .warning {
            color: orange;
            font-weight: bold;
        }
        .info {
            color: blue;
        }
        .raw-data {
            font-size: 0.85em;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1>AutoGPT.net Diagnostic Tool</h1>
        <p class="lead">This tool performs a comprehensive diagnosis of the autogpt.net WordPress API.</p>';

// Function to fetch URL with detailed diagnostics
function fetchUrlWithDiagnostics($url, $options = []) {
    echo "<div class='card mb-4'>";
    echo "<div class='card-header'><strong>Testing URL:</strong> " . htmlspecialchars($url) . "</div>";
    echo "<div class='card-body'><pre>";
    
    // Initialize cURL
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_HEADER, true); // Include headers in output
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // Set user agent
    $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
    
    // Set additional headers
    $headers = [
        'Accept: application/json',
        'Cache-Control: no-cache',
        'Pragma: no-cache'
    ];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    // Execute the request
    echo "<span class='info'>Sending request to " . htmlspecialchars($url) . "...</span>\n";
    echo "<span class='info'>User-Agent: " . htmlspecialchars($userAgent) . "</span>\n";
    echo "<span class='info'>Headers: " . implode(", ", $headers) . "</span>\n\n";
    
    $response = curl_exec($ch);
    
    // Check for errors
    if (curl_errno($ch)) {
        echo "<span class='error'>cURL Error: " . curl_error($ch) . "</span>\n";
        curl_close($ch);
        echo "</pre></div></div>";
        return false;
    }
    
    // Get response info
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
    $connectTime = curl_getinfo($ch, CURLINFO_CONNECT_TIME);
    
    // Split header and body
    $header = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    // Display response info
    echo "<span class='info'>HTTP Code: " . $httpCode . "</span>\n";
    echo "<span class='info'>Content-Type: " . $contentType . "</span>\n";
    echo "<span class='info'>Total Time: " . $totalTime . " seconds</span>\n";
    echo "<span class='info'>Connect Time: " . $connectTime . " seconds</span>\n";
    echo "<span class='info'>Response Size: " . strlen($response) . " bytes</span>\n";
    echo "<span class='info'>Header Size: " . $headerSize . " bytes</span>\n";
    echo "<span class='info'>Body Size: " . strlen($body) . " bytes</span>\n\n";
    
    // Check if response is successful
    if ($httpCode >= 200 && $httpCode < 300) {
        echo "<span class='success'>Request successful! (HTTP " . $httpCode . ")</span>\n\n";
    } else {
        echo "<span class='error'>Request failed! (HTTP " . $httpCode . ")</span>\n\n";
    }
    
    // Display headers
    echo "<strong>Response Headers:</strong>\n";
    $headerLines = explode("\r\n", $header);
    foreach ($headerLines as $line) {
        if (!empty($line)) {
            echo $line . "\n";
        }
    }
    echo "\n";
    
    // Check if it's JSON
    $isJson = false;
    if (strpos($contentType, 'application/json') !== false) {
        $isJson = true;
        echo "<span class='info'>Response is JSON</span>\n";
        
        // Try to parse JSON
        $jsonData = json_decode($body, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<span class='success'>JSON is valid</span>\n";
            
            // Check if it's an array or object
            if (is_array($jsonData)) {
                echo "<span class='info'>JSON is an array with " . count($jsonData) . " items</span>\n";
                
                // If it's a WordPress API response, check for posts
                if (count($jsonData) > 0 && isset($jsonData[0]['id']) && isset($jsonData[0]['title'])) {
                    echo "<span class='success'>Found " . count($jsonData) . " posts</span>\n\n";
                    
                    // Display first post details
                    $post = $jsonData[0];
                    echo "<strong>First Post Details:</strong>\n";
                    echo "ID: " . $post['id'] . "\n";
                    echo "Title: " . $post['title']['rendered'] . "\n";
                    echo "Date: " . $post['date'] . "\n";
                    echo "Status: " . $post['status'] . "\n";
                    echo "Link: " . $post['link'] . "\n";
                    
                    // Check if the post date is in the future
                    $postDate = strtotime($post['date']);
                    $now = time();
                    if ($postDate > $now) {
                        echo "<span class='warning'>This post has a future date! (" . date('Y-m-d H:i:s', $postDate) . ")</span>\n";
                        echo "Current time: " . date('Y-m-d H:i:s', $now) . "\n";
                        echo "Time difference: " . ($postDate - $now) . " seconds (" . round(($postDate - $now) / 86400) . " days)\n";
                    }
                } else {
                    echo "<span class='warning'>JSON does not appear to contain WordPress posts</span>\n";
                }
            } else {
                echo "<span class='info'>JSON is an object</span>\n";
                
                // Check if it's an error response
                if (isset($jsonData['code']) && isset($jsonData['message'])) {
                    echo "<span class='error'>WordPress API error: " . $jsonData['code'] . " - " . $jsonData['message'] . "</span>\n";
                }
            }
        } else {
            echo "<span class='error'>JSON is invalid: " . json_last_error_msg() . "</span>\n";
            
            // Try to clean up JSON
            $cleanBody = preg_replace('/[\x00-\x1F\x7F]/', '', $body);
            $jsonData = json_decode($cleanBody, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "<span class='success'>JSON is valid after cleanup</span>\n";
            } else {
                echo "<span class='error'>JSON is still invalid after cleanup: " . json_last_error_msg() . "</span>\n";
            }
        }
    } else {
        echo "<span class='warning'>Response is not JSON</span>\n";
    }
    
    // Display raw response (truncated)
    echo "\n<strong>Raw Response Body (first 1000 bytes):</strong>\n";
    echo "<div class='raw-data'>" . htmlspecialchars(substr($body, 0, 1000)) . "</div>\n";
    if (strlen($body) > 1000) {
        echo "<span class='info'>... (truncated)</span>\n";
    }
    
    curl_close($ch);
    echo "</pre></div></div>";
    
    return $httpCode >= 200 && $httpCode < 300 ? $body : false;
}

// Test different API endpoints
echo '<h2 class="mt-4">Testing WordPress API Endpoints</h2>';

// Test main API endpoint
fetchUrlWithDiagnostics('https://autogpt.net/wp-json/');

// Test posts endpoint with different parameters
fetchUrlWithDiagnostics('https://autogpt.net/wp-json/wp/v2/posts?per_page=1');
fetchUrlWithDiagnostics('https://autogpt.net/wp-json/wp/v2/posts?per_page=1&_embed=1');
fetchUrlWithDiagnostics('https://autogpt.net/wp-json/wp/v2/posts?per_page=1&orderby=date&order=desc');

// Test with date parameters
fetchUrlWithDiagnostics('https://autogpt.net/wp-json/wp/v2/posts?per_page=1&after=2023-01-01T00:00:00Z');
fetchUrlWithDiagnostics('https://autogpt.net/wp-json/wp/v2/posts?per_page=1&before=2026-01-01T00:00:00Z');

// HTML footer
echo '</div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
?>
