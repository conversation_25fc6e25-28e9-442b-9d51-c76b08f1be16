<?php
/**
 * Get Job Progress
 * 
 * This script returns the current progress of a running job in JSON format.
 */

// Enable error reporting
ini_set('display_errors', 0); // Disable error display for API
ini_set('display_startup_errors', 0);
error_reporting(0);

// Set content type to JSON
header('Content-Type: application/json');

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// Get job ID from query string
$jobId = isset($_GET['job_id']) ? (int)$_GET['job_id'] : 0;

if (!$jobId) {
    echo json_encode([
        'error' => 'Missing job ID',
        'status' => 'error'
    ]);
    exit;
}

// Get job data
$job = $db->getRow("SELECT * FROM jobs WHERE id = ?", [$jobId]);

if (!$job) {
    echo json_encode([
        'error' => 'Job not found',
        'status' => 'error'
    ]);
    exit;
}

// Get job run data if available
$jobRun = $db->getRow("SELECT * FROM job_runs WHERE job_id = ? ORDER BY id DESC LIMIT 1", [$jobId]);

// Prepare response
$response = [
    'job_id' => $jobId,
    'status' => $job['status'],
    'last_run' => $job['last_run'],
    'last_run_posts' => $job['last_run_posts']
];

// Add progress information if available
if ($job['status'] === 'running' && !empty($job['error'])) {
    try {
        $progressData = json_decode($job['error'], true);
        if (is_array($progressData) && isset($progressData['progress'])) {
            $response['progress'] = $progressData['progress'];
            $response['elapsed_time'] = $progressData['elapsed_time'] ?? 0;
            $response['timestamp'] = $progressData['timestamp'] ?? date('Y-m-d H:i:s');
        }
    } catch (Exception $e) {
        // Ignore JSON decode errors
    }
}

// Add job run information if available
if ($jobRun) {
    $response['run'] = [
        'id' => $jobRun['id'],
        'status' => $jobRun['status'],
        'start_time' => $jobRun['start_time'],
        'end_time' => $jobRun['end_time'],
        'posts_grabbed' => $jobRun['posts_grabbed']
    ];
    
    // Add progress information from job run if available
    if ($jobRun['status'] === 'running' && !empty($jobRun['error'])) {
        try {
            $runProgressData = json_decode($jobRun['error'], true);
            if (is_array($runProgressData) && isset($runProgressData['progress'])) {
                // Use job run progress data if more recent than job progress data
                if (!isset($response['timestamp']) || 
                    (isset($runProgressData['timestamp']) && strtotime($runProgressData['timestamp']) > strtotime($response['timestamp']))) {
                    $response['progress'] = $runProgressData['progress'];
                    $response['elapsed_time'] = $runProgressData['elapsed_time'] ?? 0;
                    $response['timestamp'] = $runProgressData['timestamp'] ?? date('Y-m-d H:i:s');
                }
            }
        } catch (Exception $e) {
            // Ignore JSON decode errors
        }
    }
}

// Return response
echo json_encode($response);
