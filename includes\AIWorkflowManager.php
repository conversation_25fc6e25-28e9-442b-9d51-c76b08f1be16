<?php
/**
 * AI Workflow Manager
 * 
 * This class manages AI workflows and their execution.
 */
class AIWorkflowManager {
    private $db;
    
    /**
     * Constructor
     * 
     * @param Database $db Database instance
     */
    public function __construct(Database $db) {
        $this->db = $db;
    }
    
    /**
     * Get all workflows
     * 
     * @param bool $activeOnly Only get active workflows
     * @return array Workflows
     */
    public function getWorkflows($activeOnly = true) {
        $sql = "SELECT * FROM ai_workflows";
        if ($activeOnly) {
            $sql .= " WHERE is_active = 1";
        }
        $sql .= " ORDER BY name";
        
        return $this->db->query($sql);
    }
    
    /**
     * Get workflow by ID
     * 
     * @param int $id Workflow ID
     * @return array|null Workflow data or null if not found
     */
    public function getWorkflow($id) {
        return $this->db->getRow("SELECT * FROM ai_workflows WHERE id = ?", [$id]);
    }
    
    /**
     * Get default workflow
     * 
     * @return array|null Default workflow or null if not found
     */
    public function getDefaultWorkflow() {
        return $this->db->getRow("SELECT * FROM ai_workflows WHERE is_default = 1 AND is_active = 1");
    }
    
    /**
     * Get workflow steps
     * 
     * @param int $workflowId Workflow ID
     * @param string $stepType Optional step type filter
     * @return array Workflow steps
     */
    public function getWorkflowSteps($workflowId, $stepType = null) {
        $sql = "SELECT ws.*, m.name as model_name, m.model_id as model_identifier, m.type as model_type, 
                p.name as provider_name, p.slug as provider_slug
                FROM ai_workflow_steps ws
                JOIN ai_models m ON ws.model_id = m.id
                JOIN ai_providers p ON m.provider_id = p.id
                WHERE ws.workflow_id = ? AND ws.is_active = 1";
        
        $params = [$workflowId];
        
        if ($stepType) {
            $sql .= " AND ws.step_type = ?";
            $params[] = $stepType;
        }
        
        $sql .= " ORDER BY ws.order_index";
        
        return $this->db->query($sql, $params);
    }
    
    /**
     * Create a new workflow
     * 
     * @param array $data Workflow data
     * @return int Workflow ID
     */
    public function createWorkflow($data) {
        // Validate required fields
        if (empty($data['name'])) {
            throw new Exception("Workflow name is required");
        }
        
        // Set default values
        $data['is_active'] = $data['is_active'] ?? 1;
        $data['is_default'] = $data['is_default'] ?? 0;
        
        // If this workflow is set as default, unset any existing default
        if ($data['is_default']) {
            $this->db->query("UPDATE ai_workflows SET is_default = 0 WHERE is_default = 1");
        }
        
        // Insert workflow
        return $this->db->insert('ai_workflows', $data);
    }
    
    /**
     * Update a workflow
     * 
     * @param int $id Workflow ID
     * @param array $data Workflow data
     * @return bool Success status
     */
    public function updateWorkflow($id, $data) {
        // If this workflow is set as default, unset any existing default
        if (!empty($data['is_default']) && $data['is_default']) {
            $this->db->query("UPDATE ai_workflows SET is_default = 0 WHERE is_default = 1 AND id != ?", [$id]);
        }
        
        // Update workflow
        return $this->db->update('ai_workflows', $data, 'id = ?', [$id]);
    }
    
    /**
     * Delete a workflow
     * 
     * @param int $id Workflow ID
     * @return bool Success status
     */
    public function deleteWorkflow($id) {
        // Check if workflow is default
        $workflow = $this->getWorkflow($id);
        if ($workflow && $workflow['is_default']) {
            throw new Exception("Cannot delete the default workflow");
        }
        
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Delete workflow steps
            $this->db->delete('ai_workflow_steps', 'workflow_id = ?', [$id]);
            
            // Delete workflow
            $this->db->delete('ai_workflows', 'id = ?', [$id]);
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Add a step to a workflow
     * 
     * @param int $workflowId Workflow ID
     * @param array $data Step data
     * @return int Step ID
     */
    public function addWorkflowStep($workflowId, $data) {
        // Validate required fields
        if (empty($data['model_id']) || empty($data['step_type']) || empty($data['task_type'])) {
            throw new Exception("Model ID, step type, and task type are required");
        }
        
        // Set workflow ID
        $data['workflow_id'] = $workflowId;
        
        // Set order index if not provided
        if (empty($data['order_index'])) {
            $maxOrder = $this->db->getValue("SELECT MAX(order_index) FROM ai_workflow_steps WHERE workflow_id = ?", [$workflowId]);
            $data['order_index'] = $maxOrder ? $maxOrder + 1 : 1;
        }
        
        // Insert step
        return $this->db->insert('ai_workflow_steps', $data);
    }
    
    /**
     * Update a workflow step
     * 
     * @param int $id Step ID
     * @param array $data Step data
     * @return bool Success status
     */
    public function updateWorkflowStep($id, $data) {
        return $this->db->update('ai_workflow_steps', $data, 'id = ?', [$id]);
    }
    
    /**
     * Delete a workflow step
     * 
     * @param int $id Step ID
     * @return bool Success status
     */
    public function deleteWorkflowStep($id) {
        return $this->db->delete('ai_workflow_steps', 'id = ?', [$id]);
    }
    
    /**
     * Reorder workflow steps
     * 
     * @param int $workflowId Workflow ID
     * @param array $stepIds Ordered array of step IDs
     * @return bool Success status
     */
    public function reorderWorkflowSteps($workflowId, $stepIds) {
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            foreach ($stepIds as $index => $stepId) {
                $this->db->update('ai_workflow_steps', 
                    ['order_index' => $index + 1], 
                    'id = ? AND workflow_id = ?', 
                    [$stepId, $workflowId]
                );
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            throw $e;
        }
    }
}
?>
