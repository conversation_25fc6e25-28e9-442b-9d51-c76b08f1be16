<?php
/**
 * NOVITA AI Provider
 *
 * This class implements the NOVITA AI provider.
 * NOVITA AI provides both text and image generation capabilities.
 */
class NovitaAIProvider extends AIProvider {
    // Base API URL
    private $apiBaseUrl = 'https://api.novita.ai/v1';

    /**
     * Generate text using NOVITA AI
     *
     * @param string $prompt The prompt to send to the API
     * @param array $options Additional options for the API call
     * @return array Response data including generated text and metadata
     */
    public function generateText($prompt, $options = []) {
        // Get model ID from options or use default
        $modelId = $options['model_id'] ?? null;
        if (!$modelId) {
            throw new Exception("Model ID is required for text generation");
        }

        // Get model data
        $model = $this->db->getRow("SELECT * FROM ai_models WHERE id = ? AND is_active = 1", [$modelId]);
        if (!$model) {
            throw new Exception("Model not found or not active");
        }

        // Prepare request data
        $requestData = [
            'model' => $model['model_id'],
            'prompt' => $prompt,
            'max_tokens' => $options['max_tokens'] ?? $model['max_tokens'] ?? 4096,
            'temperature' => $options['temperature'] ?? $model['temperature'] ?? 0.7,
        ];

        // Add system prompt if available
        if (!empty($model['system_prompt'])) {
            $requestData['system'] = $model['system_prompt'];
        }

        // Start timer
        $startTime = microtime(true);

        // Make API request
        $response = $this->makeApiRequest('/text/completions', $requestData);

        // Calculate execution time
        $executionTime = microtime(true) - $startTime;

        // Process response
        $processedResponse = [
            'text' => $response['choices'][0]['text'] ?? '',
            'tokens_used' => $response['usage']['total_tokens'] ?? 0,
            'model' => $response['model'] ?? $model['model_id'],
            'execution_time' => $executionTime,
            'raw_response' => $response
        ];

        // Log API usage if processed post ID is provided
        if (!empty($options['processed_post_id'])) {
            $this->logApiUsage(
                $options['processed_post_id'],
                $options['step_type'] ?? 'content',
                $options['task_type'] ?? 'generate',
                $modelId,
                $prompt,
                json_encode($response),
                $processedResponse['tokens_used'],
                $executionTime
            );
        }

        return $processedResponse;
    }

    /**
     * Generate image using NOVITA AI
     *
     * @param string $prompt The prompt to send to the API
     * @param array $options Additional options for the API call
     * @return array Response data including image URL and metadata
     */
    public function generateImage($prompt, $options = []) {
        // Get model ID from options or use default
        $modelId = $options['model_id'] ?? null;
        if (!$modelId) {
            throw new Exception("Model ID is required for image generation");
        }

        // Get model data
        $model = $this->db->getRow("SELECT * FROM ai_models WHERE id = ? AND is_active = 1", [$modelId]);
        if (!$model) {
            throw new Exception("Model not found or not active");
        }

        // Prepare request data
        $requestData = [
            'model' => $model['model_id'],
            'prompt' => $prompt,
            'n' => $options['n'] ?? 1,
            'size' => $options['size'] ?? '1024x1024',
            'response_format' => $options['response_format'] ?? 'url',
        ];

        // Start timer
        $startTime = microtime(true);

        // Make API request
        $response = $this->makeApiRequest('/images/generations', $requestData);

        // Calculate execution time
        $executionTime = microtime(true) - $startTime;

        // Process response
        $processedResponse = [
            'images' => $response['data'] ?? [],
            'execution_time' => $executionTime,
            'raw_response' => $response
        ];

        // Log API usage if processed post ID is provided
        if (!empty($options['processed_post_id'])) {
            $this->logApiUsage(
                $options['processed_post_id'],
                $options['step_type'] ?? 'image_generation',
                $options['task_type'] ?? 'generate',
                $modelId,
                $prompt,
                json_encode($response),
                0, // No tokens for image generation
                $executionTime
            );
        }

        return $processedResponse;
    }

    /**
     * Make API request to NOVITA AI
     *
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @return array Response data
     */
    private function makeApiRequest($endpoint, $data) {
        $url = $this->apiBaseUrl . $endpoint;

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            throw new Exception('API request failed: ' . curl_error($ch));
        }

        curl_close($ch);

        $responseData = json_decode($response, true);

        if ($httpCode >= 400) {
            throw new Exception('API request failed with code ' . $httpCode . ': ' . ($responseData['error']['message'] ?? 'Unknown error'));
        }

        return $responseData;
    }
}
?>
