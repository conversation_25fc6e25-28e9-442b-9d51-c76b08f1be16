<?php
/**
 * Run Job Script
 * 
 * This script runs a job directly from the command line.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/Database.php';
require_once 'includes/JobManager.php';

// Initialize database connection
$db = new Database();

// Initialize JobManager
$jobManager = new JobManager($db);

// Check if job ID is provided
$jobId = isset($argv[1]) ? (int)$argv[1] : 0;

if ($jobId <= 0) {
    // Get all jobs
    $jobs = $jobManager->getJobs();
    
    if (empty($jobs)) {
        echo "No jobs found. Please create a job first.\n";
        exit;
    }
    
    // Display jobs
    echo "Available jobs:\n";
    foreach ($jobs as $job) {
        echo "- ID: {$job['id']}, Name: {$job['name']}, URL: {$job['url']}\n";
    }
    
    // Ask for job ID
    echo "\nEnter the ID of the job you want to run: ";
    $jobId = trim(fgets(STDIN));
}

// Get job
$job = $jobManager->getJob($jobId);

if (!$job) {
    echo "Job with ID $jobId not found.\n";
    exit;
}

echo "Running job: {$job['name']} (ID: $jobId)\n";
echo "URL: {$job['url']}\n";
echo "Type: {$job['type']}\n";
echo "Posts per run: {$job['posts_per_run']}\n";

// Run the job
echo "\nRunning job...\n";
$result = $jobManager->runJob($jobId);

// Display result
if ($result['success']) {
    echo "Job ran successfully! Grabbed {$result['posts_count']} posts.\n";
} else {
    echo "Job failed: {$result['error']}\n";
}

// Display posts
$posts = $db->query("SELECT * FROM posts WHERE job_id = ? ORDER BY date_published DESC", [$jobId]);

if (empty($posts)) {
    echo "No posts found for this job.\n";
    exit;
}

echo "\nPosts grabbed:\n";
foreach ($posts as $post) {
    $futureStatus = isset($post['is_future']) && $post['is_future'] ? 'FUTURE' : 'CURRENT';
    echo "- ID: {$post['id']}, Title: {$post['title']}, Date: {$post['date_published']} ($futureStatus)\n";
}
?>
