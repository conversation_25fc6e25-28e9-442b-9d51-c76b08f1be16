<?php
/**
 * Main entry point for Content Grabber
 */

// Load configuration
require_once 'config.php';

// Check if the application is installed
if (!is_installed()) {
    // Redirect to setup wizard
    header('Location: ' . BASE_URL . '/setup/');
    exit;
}

// Create content directories
create_content_directories();

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page
    header('Location: ' . BASE_URL . '/login.php');
    exit;
}

// Load required files
require_once 'includes/Database.php';
require_once 'includes/ContentGrabber.php';
require_once 'includes/JobManager.php';

// Initialize database connection
$db = new Database();

// Check if database needs update
$needsUpdate = false;
$currentDbVersion = '1.1'; // Current database version

try {
    // Check if settings table exists
    $settingsTable = $db->query("SHOW TABLES LIKE 'settings'");
    if (!empty($settingsTable)) {
        try {
            // Get stored database version
            $storedVersion = $db->getValue("SELECT value FROM settings WHERE `key` = 'db_version'");
            if ($storedVersion && version_compare($storedVersion, $currentDbVersion, '<')) {
                $needsUpdate = true;
            }
        } catch (Exception $e) {
            // If there's an error with the query, assume we need to update
            $needsUpdate = true;
        }
    } else {
        // No settings table means we need to update
        $needsUpdate = true;
    }
} catch (Exception $e) {
    // If there's an error, assume we need to update
    $needsUpdate = true;
}

// If database needs update and we're not already on the update page
if ($needsUpdate && $page !== 'settings' && $action !== 'database_update') {
    // Show database update notification
    $_SESSION['db_update_required'] = true;

    // If this is an admin user, redirect to the database update page
    if (isset($currentUser['role']) && $currentUser['role'] === 'admin') {
        header('Location: ' . BASE_URL . '/?page=settings&action=database_update');
        exit;
    }
}

// Get current user
$currentUser = $db->getRow("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);

// Handle page routing
$page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Validate page
$validPages = ['dashboard', 'jobs', 'posts', 'settings', 'process', 'analytics', 'users', 'ai_settings'];
if (!in_array($page, $validPages)) {
    $page = 'dashboard';
}

// Check permissions for restricted pages
if ($page === 'users') {
    try {
        if (file_exists('includes/UserManager.php')) {
            require_once 'includes/UserManager.php';
            $userManager = new UserManager($db);

            if (!$userManager->hasPermission($_SESSION['user_id'], 'manage_users')) {
                // Redirect to dashboard if user doesn't have permission
                header('Location: ' . BASE_URL . '/');
                exit;
            }
        } else {
            // Redirect to dashboard if UserManager is not available
            header('Location: ' . BASE_URL . '/');
            exit;
        }
    } catch (Exception $e) {
        // Redirect to dashboard if there's an error
        header('Location: ' . BASE_URL . '/');
        exit;
    }
}

// Start output buffering
ob_start();

// Include header
include 'templates/header.php';

// Load the requested page
switch ($page) {
    case 'jobs':
        include 'templates/jobs.php';
        break;
    case 'posts':
        include 'templates/posts.php';
        break;
    case 'process':
        include 'templates/process.php';
        break;
    case 'settings':
        include 'templates/settings.php';
        break;
    case 'analytics':
        include 'templates/analytics.php';
        break;
    case 'users':
        include 'templates/users.php';
        break;
    case 'ai_settings':
        include 'templates/ai_settings.php';
        break;
    default:
        include 'templates/dashboard.php';
        break;
}

// Include footer
include 'templates/footer.php';

// End output buffering and send to browser
ob_end_flush();
