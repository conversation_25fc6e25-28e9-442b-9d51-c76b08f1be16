<?php
/**
 * Base ContentGrabber class
 *
 * This is the base class for all content grabbers.
 */
class ContentGrabber {
    protected $db;
    protected $url;
    protected $options;

    /**
     * Constructor
     *
     * @param Database $db Database instance
     * @param string $url URL to grab content from
     * @param array $options Grabbing options
     */
    public function __construct(Database $db, $url, $options = []) {
        $this->db = $db;
        $this->url = $url;
        $this->options = $options;
    }

    /**
     * Factory method to create the appropriate grabber
     *
     * @param Database $db Database instance
     * @param string $url URL to grab content from
     * @param array $options Grabbing options
     * @return ContentGrabber Appropriate grabber instance
     */
    public static function create(Database $db, $url, $options = []) {
        // Debug mode
        $debug = isset($options['debug']) && $options['debug'];

        if ($debug) {
            error_log("ContentGrabber: Creating grabber for URL: $url");
        }

        // Check if it's a WordPress site with REST API
        if (self::isWordPressSite($url, ['debug' => $debug])) {
            if ($debug) {
                error_log("ContentGrabber: Detected WordPress site, creating EnhancedWordPressGrabber");
            }
            require_once 'EnhancedWordPressGrabber.php';
            return new EnhancedWordPressGrabber($db, $url, $options);
        }

        // Check if it's a sitemap
        if (self::isSitemap($url, ['debug' => $debug])) {
            if ($debug) {
                error_log("ContentGrabber: Detected sitemap, creating EnhancedSitemapGrabber");
            }
            require_once 'EnhancedSitemapGrabber.php';
            return new EnhancedSitemapGrabber($db, $url, $options);
        }

        // Default to base grabber
        if ($debug) {
            error_log("ContentGrabber: No specific grabber detected, using base ContentGrabber");
        }
        return new self($db, $url, $options);
    }

    /**
     * Check if the URL is a WordPress site with REST API
     *
     * @param string $url URL to check
     * @return bool True if WordPress site with REST API
     */
    public static function isWordPressSite($url, $options = []) {
        // Debug mode
        $debug = isset($options['debug']) && $options['debug'];

        if ($debug) {
            error_log("ContentGrabber: Checking if $url is a WordPress site");
        }

        // Try to access the WordPress REST API
        $apiUrl = rtrim($url, '/') . '/wp-json/';
        $response = self::fetchUrl($apiUrl, ['debug' => $debug]);

        // If we get a valid JSON response, it's a WordPress site with REST API
        $isValid = $response && json_decode($response) !== null;

        if ($debug) {
            error_log("ContentGrabber: WordPress API check for $apiUrl: " . ($isValid ? 'Valid' : 'Invalid'));
        }

        // If not valid, try the wp/v2/posts endpoint directly
        if (!$isValid) {
            $postsUrl = rtrim($url, '/') . '/wp-json/wp/v2/posts';
            $response = self::fetchUrl($postsUrl, ['debug' => $debug]);
            $isValid = $response && json_decode($response) !== null;

            if ($debug) {
                error_log("ContentGrabber: WordPress posts check for $postsUrl: " . ($isValid ? 'Valid' : 'Invalid'));
            }
        }

        return $isValid;
    }

    /**
     * Check if the URL is a sitemap
     *
     * @param string $url URL to check
     * @return bool True if sitemap
     */
    public static function isSitemap($url, $options = []) {
        // Debug mode
        $debug = isset($options['debug']) && $options['debug'];

        if ($debug) {
            error_log("ContentGrabber: Checking if $url is a sitemap");
        }

        // Check if the URL ends with sitemap.xml
        if (preg_match('/sitemap\.xml$/i', $url)) {
            if ($debug) {
                error_log("ContentGrabber: URL ends with sitemap.xml, assuming it's a sitemap");
            }
            return true;
        }

        // Try to fetch the URL and check if it's a valid XML sitemap
        $response = self::fetchUrl($url, ['debug' => $debug]);
        if (!$response) {
            if ($debug) {
                error_log("ContentGrabber: Failed to fetch URL for sitemap check");
            }
            return false;
        }

        // Check if it's a valid XML and contains sitemap elements
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($response);
        $errors = libxml_get_errors();
        libxml_clear_errors();

        if ($xml === false || count($errors) > 0) {
            if ($debug) {
                error_log("ContentGrabber: URL content is not valid XML");
            }
            return false;
        }

        // Check for sitemap namespace or elements
        $isSitemap = strpos($response, '<urlset') !== false || strpos($response, '<sitemapindex') !== false;

        if ($debug) {
            error_log("ContentGrabber: Sitemap check result: " . ($isSitemap ? 'Yes' : 'No'));
        }

        return $isSitemap;
    }

    /**
     * Fetch URL content
     *
     * @param string $url URL to fetch
     * @param array $options Additional options for the request
     * @return string|bool Content or false on failure
     */
    public static function fetchUrl($url, $options = []) {
        // Check if it's autogpt.net - it requires special handling
        $isAutogpt = (strpos($url, 'autogpt.net') !== false);

        // Debug mode
        $debug = isset($options['debug']) && $options['debug'];

        if ($debug) {
            error_log("ContentGrabber: Fetching URL: $url" . ($isAutogpt ? " (autogpt.net site)" : ""));
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        // Add headers for autogpt.net
        if ($isAutogpt) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Cache-Control: no-cache',
                'Pragma: no-cache'
            ]);
        }

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

        if ($debug) {
            error_log("ContentGrabber: HTTP Code: $httpCode, Content-Type: $contentType, Response length: " . strlen($response));
        }

        // Check for errors
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            if ($debug) {
                error_log("ContentGrabber: cURL error: $error");
            }
            curl_close($ch);
            return false;
        }

        curl_close($ch);

        // Special handling for autogpt.net
        if ($isAutogpt && $httpCode >= 200 && $httpCode < 300) {
            // Make sure it's valid JSON
            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                if ($debug) {
                    error_log("ContentGrabber: Invalid JSON response from autogpt.net");
                    error_log("ContentGrabber: JSON error: " . json_last_error_msg());
                    error_log("ContentGrabber: Response: " . substr($response, 0, 500) . "...");
                }

                // Try to fix common JSON issues
                $response = preg_replace('/[\x00-\x1F\x7F]/', '', $response);
                $data = json_decode($response, true);
                if (json_last_error() !== JSON_ERROR_NONE && $debug) {
                    error_log("ContentGrabber: Still invalid JSON after cleanup");
                }
            }
        }

        return ($httpCode >= 200 && $httpCode < 300) ? $response : false;
    }

    /**
     * Grab content from the URL
     *
     * @param int $limit Maximum number of posts to grab
     * @return array Grabbed content with stats
     */
    public function grab($limit = 10) {
        // Base implementation - to be overridden by subclasses
        return [
            'posts' => [],
            'stats' => [
                'total' => 0,
                'new' => 0,
                'updated' => 0,
                'skipped' => 0,
                'execution_time' => 0,
                'approaches_tried' => 0
            ]
        ];
    }

    /**
     * Download an image from a URL
     *
     * @param string $url Image URL
     * @param string $postDir Directory to save the image in
     * @return string|bool Local path to the image or false on failure
     */
    protected function downloadImage($url, $postDir) {
        try {
            // Validate URL
            if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
                error_log("ContentGrabber: Invalid image URL: $url");
                return false;
            }

            // Create the post directory if it doesn't exist
            if (!file_exists($postDir)) {
                if (!mkdir($postDir, 0755, true)) {
                    error_log("ContentGrabber: Failed to create directory: $postDir");
                    return false;
                }
            }

            // Get the image filename
            $filename = basename(parse_url($url, PHP_URL_PATH));
            $filename = preg_replace('/[^a-zA-Z0-9\-\_\.]/', '', $filename);

            // If filename is empty or invalid, generate a random one
            if (empty($filename) || strpos($filename, '.') === false) {
                $filename = md5($url) . '.jpg';
            }

            // Full path to save the image
            $savePath = $postDir . '/' . $filename;

            // Check if the image already exists
            if (file_exists($savePath)) {
                return $savePath;
            }

            // Download the image
            $imageContent = self::fetchUrl($url);
            if ($imageContent === false) {
                error_log("ContentGrabber: Failed to download image: $url");
                return false;
            }

            // Verify it's actually an image
            if (strlen($imageContent) < 100) {
                error_log("ContentGrabber: Downloaded content too small to be an image: $url");
                return false;
            }

            // Check image signature
            $signatures = [
                'jpeg' => "\xFF\xD8\xFF",
                'png'  => "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A",
                'gif'  => "GIF",
                'bmp'  => "BM",
                'webp' => "RIFF"
            ];

            $isImage = false;
            foreach ($signatures as $format => $signature) {
                if (strpos($imageContent, $signature) === 0) {
                    $isImage = true;
                    break;
                }
            }

            if (!$isImage) {
                error_log("ContentGrabber: Downloaded content is not a valid image: $url");
                return false;
            }

            // Save the image
            if (file_put_contents($savePath, $imageContent) === false) {
                error_log("ContentGrabber: Failed to save image to: $savePath");
                return false;
            }

            return $savePath;
        } catch (Exception $e) {
            error_log("ContentGrabber: Exception while downloading image: " . $e->getMessage());
            return false;
        }
    }
}
