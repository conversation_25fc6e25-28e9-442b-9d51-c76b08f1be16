<?php
/**
 * Database Update Script
 *
 * This script updates the database schema to add missing columns and tables.
 */

// Load configuration
require_once 'config.php';

// Check if the application is installed
if (!is_installed()) {
    echo "Application not installed. Please run the setup wizard first.\n";
    exit(1);
}

// Load required files
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// Start transaction
$db->beginTransaction();

try {
    // Check if jobs table exists
    $jobsTableExists = false;
    try {
        $result = $db->query("SHOW TABLES LIKE 'jobs'");
        $jobsTableExists = !empty($result);
    } catch (Exception $e) {
        // Ignore error
    }

    // Create or update jobs table
    if (!$jobsTableExists) {
        $createJobsSql = "
            CREATE TABLE `jobs` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `url` varchar(255) NOT NULL,
                `category_id` int(11) DEFAULT NULL,
                `type` enum('wordpress','sitemap') NOT NULL DEFAULT 'wordpress',
                `category` varchar(255) DEFAULT NULL,
                `disable_embed` tinyint(1) NOT NULL DEFAULT 0,
                `posts_per_run` int(11) NOT NULL DEFAULT 10,
                `after_date` date DEFAULT NULL,
                `before_date` date DEFAULT NULL,
                `status` enum('pending','running','completed','failed') NOT NULL DEFAULT 'pending',
                `schedule_type` varchar(50) DEFAULT NULL,
                `frequency` varchar(50) DEFAULT 'daily',
                `last_run` datetime DEFAULT NULL,
                `last_run_posts` int(11) DEFAULT 0,
                `error` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `category_id` (`category_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $db->query($createJobsSql);
        echo "Created jobs table.\n";
    } else {
        // Update jobs table with new columns
        $alterJobsSql = "
            ALTER TABLE `jobs`
            ADD COLUMN IF NOT EXISTS `category_id` int(11) DEFAULT NULL AFTER `url`,
            ADD COLUMN IF NOT EXISTS `disable_embed` tinyint(1) NOT NULL DEFAULT 0 AFTER `category`,
            ADD COLUMN IF NOT EXISTS `frequency` varchar(50) DEFAULT 'daily' AFTER `schedule_type`,
            ADD COLUMN IF NOT EXISTS `last_run_posts` int(11) DEFAULT 0 AFTER `last_run`,
            ADD COLUMN IF NOT EXISTS `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`,
            ADD KEY IF NOT EXISTS `category_id` (`category_id`)
        ";
        $db->query($alterJobsSql);
        echo "Updated jobs table with new columns.\n";
    }

    // Check if posts table exists
    $postsTableExists = false;
    try {
        $result = $db->query("SHOW TABLES LIKE 'posts'");
        $postsTableExists = !empty($result);
    } catch (Exception $e) {
        // Ignore error
    }

    // Create or update posts table
    if (!$postsTableExists) {
        $createPostsSql = "
            CREATE TABLE `posts` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `job_id` int(11) NOT NULL,
                `external_id` varchar(255) NOT NULL,
                `title` varchar(255) NOT NULL,
                `content` longtext NOT NULL,
                `excerpt` text DEFAULT NULL,
                `url` varchar(255) NOT NULL,
                `slug` varchar(255) NOT NULL,
                `date_published` datetime NOT NULL,
                `date_modified` datetime NOT NULL,
                `featured_image` text DEFAULT NULL,
                `html_file` varchar(255) DEFAULT NULL,
                `pdf_file` varchar(255) DEFAULT NULL,
                `metadata` text DEFAULT NULL,
                `processed` tinyint(1) NOT NULL DEFAULT 0,
                `is_future` tinyint(1) NOT NULL DEFAULT 0,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `external_id` (`external_id`),
                UNIQUE KEY `url` (`url`),
                KEY `job_id` (`job_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $db->query($createPostsSql);
        echo "Created posts table.\n";
    } else {
        // Update posts table with new columns
        $alterPostsSql = "
            ALTER TABLE `posts`
            ADD COLUMN IF NOT EXISTS `metadata` TEXT NULL AFTER `pdf_file`,
            ADD COLUMN IF NOT EXISTS `processed` TINYINT(1) NOT NULL DEFAULT 0 AFTER `metadata`,
            ADD COLUMN IF NOT EXISTS `is_future` TINYINT(1) NOT NULL DEFAULT 0 AFTER `processed`,
            MODIFY COLUMN IF EXISTS `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            ADD COLUMN IF NOT EXISTS `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`
        ";
        $db->query($alterPostsSql);
        echo "Updated posts table with new columns.\n";
    }

    // Check if images table exists
    $imagesTableExists = false;
    try {
        $result = $db->query("SHOW TABLES LIKE 'images'");
        $imagesTableExists = !empty($result);
    } catch (Exception $e) {
        // Ignore error
    }

    // Create or update images table
    if (!$imagesTableExists) {
        $createImagesSql = "
            CREATE TABLE `images` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `post_id` int(11) NOT NULL,
                `url` varchar(255) NOT NULL,
                `local_path` varchar(255) NOT NULL,
                `optimized_path` varchar(255) DEFAULT NULL,
                `alt` varchar(255) DEFAULT NULL,
                `caption` text DEFAULT NULL,
                `is_featured` tinyint(1) NOT NULL DEFAULT 0,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `post_id` (`post_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $db->query($createImagesSql);
        echo "Created images table.\n";
    } else {
        // Update images table with new columns
        $alterImagesSql = "
            ALTER TABLE `images`
            ADD COLUMN IF NOT EXISTS `optimized_path` VARCHAR(255) NULL AFTER `local_path`,
            ADD COLUMN IF NOT EXISTS `caption` text DEFAULT NULL AFTER `alt`,
            ADD COLUMN IF NOT EXISTS `is_featured` tinyint(1) NOT NULL DEFAULT 0 AFTER `caption`,
            MODIFY COLUMN IF EXISTS `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            ADD COLUMN IF NOT EXISTS `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`
        ";
        $db->query($alterImagesSql);
        echo "Updated images table with new columns.\n";
    }

    // Add missing columns to users table
    $alterUsersSql = "
        ALTER TABLE `users`
        ADD COLUMN IF NOT EXISTS `role` ENUM('admin', 'editor', 'viewer') NOT NULL DEFAULT 'viewer' AFTER `email`,
        ADD COLUMN IF NOT EXISTS `api_key` VARCHAR(255) NULL AFTER `role`,
        ADD COLUMN IF NOT EXISTS `remember_token` VARCHAR(255) NULL AFTER `api_key`,
        ADD COLUMN IF NOT EXISTS `token_expires` DATETIME NULL AFTER `remember_token`,
        ADD COLUMN IF NOT EXISTS `last_login` DATETIME NULL AFTER `token_expires`,
        MODIFY COLUMN IF EXISTS `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        ADD COLUMN IF NOT EXISTS `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`
    ";

    $db->query($alterUsersSql);
    echo "Added missing columns to users table.\n";

    // Set all existing users to admin role if they don't have a role
    $db->query("UPDATE `users` SET `role` = 'admin' WHERE `role` = 'viewer'");
    echo "Set all existing users to admin role.\n";

    // Create job_runs table if it doesn't exist
    $createJobRunsSql = "
        CREATE TABLE IF NOT EXISTS `job_runs` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `job_id` INT(11) NOT NULL,
            `status` ENUM('pending', 'running', 'completed', 'failed') NOT NULL DEFAULT 'pending',
            `start_time` DATETIME NOT NULL,
            `end_time` DATETIME NULL,
            `posts_grabbed` INT(11) NOT NULL DEFAULT 0,
            `error` TEXT NULL,
            `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `job_id` (`job_id`),
            KEY `status` (`status`),
            KEY `start_time` (`start_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $db->query($createJobRunsSql);
    echo "Created job_runs table.\n";

    // Create job_stats table if it doesn't exist
    $createJobStatsSql = "
        CREATE TABLE IF NOT EXISTS `job_stats` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `job_id` INT(11) NOT NULL,
            `run_date` DATETIME NOT NULL,
            `total_posts` INT(11) NOT NULL DEFAULT 0,
            `new_posts` INT(11) NOT NULL DEFAULT 0,
            `updated_posts` INT(11) NOT NULL DEFAULT 0,
            `skipped_posts` INT(11) NOT NULL DEFAULT 0,
            `execution_time` FLOAT NOT NULL DEFAULT 0,
            `approaches_tried` INT(11) NOT NULL DEFAULT 0,
            `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `job_id` (`job_id`),
            KEY `run_date` (`run_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $db->query($createJobStatsSql);
    echo "Created job_stats table.\n";

    // Create settings table if it doesn't exist
    $createSettingsSql = "
        CREATE TABLE IF NOT EXISTS `settings` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `key` VARCHAR(255) NOT NULL,
            `value` TEXT DEFAULT NULL,
            `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `key` (`key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $db->query($createSettingsSql);
    echo "Created settings table.\n";

    // Commit transaction
    $db->commit();

    echo "Database update completed successfully!\n";
} catch (Exception $e) {
    // Rollback transaction
    $db->rollback();

    echo "Error updating database: " . $e->getMessage() . "\n";
    exit(1);
}
