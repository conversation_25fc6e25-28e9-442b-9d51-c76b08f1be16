<?php
// Get providers
$providers = $db->query("SELECT * FROM ai_providers ORDER BY name");

// Handle edit action
if ($action === 'edit' && $id > 0) {
    $provider = $db->getRow("SELECT * FROM ai_providers WHERE id = ?", [$id]);
    if (!$provider) {
        echo '<div class="alert alert-danger">Provider not found.</div>';
        $action = '';
    }
}
?>

<div class="row">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo $action === 'edit' ? 'Edit Provider' : 'Add Provider'; ?></h5>
                <?php if ($action === 'edit'): ?>
                <span class="badge bg-<?php echo $provider['is_active'] ? 'success' : 'danger'; ?>">
                    <?php echo $provider['is_active'] ? 'Active' : 'Inactive'; ?>
                </span>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?php echo $action === 'edit' ? 'update_provider' : 'add_provider'; ?>">
                    <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="provider_id" value="<?php echo $provider['id']; ?>">
                    <?php endif; ?>

                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-robot text-primary me-1"></i> Provider Name
                        </label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo $action === 'edit' ? htmlspecialchars($provider['name']) : ''; ?>" required>
                        <div class="form-text">The name of the AI provider (e.g., NOVITA AI, OpenAI)</div>
                    </div>

                    <div class="mb-3">
                        <label for="slug" class="form-label">
                            <i class="fas fa-link text-primary me-1"></i> Provider Slug
                        </label>
                        <input type="text" class="form-control" id="slug" name="slug" value="<?php echo $action === 'edit' ? htmlspecialchars($provider['slug']) : ''; ?>" <?php echo $action === 'edit' ? 'readonly' : 'required'; ?>>
                        <div class="form-text">A unique identifier for the provider (e.g., novita, openai)</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-info-circle text-primary me-1"></i> Description
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo $action === 'edit' ? htmlspecialchars($provider['description']) : ''; ?></textarea>
                        <div class="form-text">A brief description of the provider's capabilities</div>
                    </div>

                    <div class="mb-3">
                        <label for="website" class="form-label">
                            <i class="fas fa-globe text-primary me-1"></i> Website
                        </label>
                        <input type="url" class="form-control" id="website" name="website" value="<?php echo $action === 'edit' ? htmlspecialchars($provider['website']) : ''; ?>">
                        <div class="form-text">The provider's website URL</div>
                    </div>

                    <?php if ($action === 'edit'): ?>
                    <div class="mb-3 form-check form-switch">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" <?php echo $provider['is_active'] ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_active">Active</label>
                        <div class="form-text">Inactive providers will not be available for use</div>
                    </div>
                    <?php endif; ?>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> <?php echo $action === 'edit' ? 'Update' : 'Add'; ?> Provider
                        </button>
                        
                        <?php if ($action === 'edit'): ?>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteProviderModal">
                            <i class="fas fa-trash-alt me-1"></i> Delete
                        </button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">AI Providers</h5>
                
                <?php if ($action === 'edit'): ?>
                <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=providers" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-plus me-1"></i> Add New
                </a>
                <?php endif; ?>
            </div>
            <div class="card-body p-0">
                <?php if (empty($providers)): ?>
                <div class="p-4 text-center">
                    <p class="text-muted mb-0">No AI providers found. Add your first provider to start using AI features.</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th>Provider</th>
                                <th>Description</th>
                                <th>Models</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($providers as $p): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-box bg-primary bg-opacity-10 text-primary rounded p-2 me-2">
                                            <i class="fas fa-robot"></i>
                                        </div>
                                        <div>
                                            <strong><?php echo htmlspecialchars($p['name']); ?></strong>
                                            <div class="small text-muted"><?php echo htmlspecialchars($p['slug']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($p['description'] ?? ''); ?></td>
                                <td>
                                    <?php 
                                    $modelCount = $db->getValue("SELECT COUNT(*) FROM ai_models WHERE provider_id = ?", [$p['id']]);
                                    echo $modelCount;
                                    ?>
                                </td>
                                <td>
                                    <?php if ($p['is_active']): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=providers&action=edit&id=<?php echo $p['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($action === 'edit'): ?>
<!-- Delete Provider Modal -->
<div class="modal fade" id="deleteProviderModal" tabindex="-1" aria-labelledby="deleteProviderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteProviderModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the provider "<?php echo htmlspecialchars($provider['name']); ?>"?</p>
                <p class="text-danger">This action cannot be undone. All API keys and models associated with this provider will also be deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="action" value="delete_provider">
                    <input type="hidden" name="provider_id" value="<?php echo $provider['id']; ?>">
                    <button type="submit" class="btn btn-danger">Delete Provider</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
