/**
 * Beautiful Job Progress Modal System
 */

class JobProgressModal {
    constructor() {
        this.modal = null;
        this.jobId = null;
        this.progressInterval = null;
        this.isRunning = false;
        this.createModal();
    }

    createModal() {
        // Create modal HTML
        const modalHTML = `
        <div class="modal fade" id="jobProgressModal" tabindex="-1" aria-labelledby="jobProgressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content border-0 shadow-lg">
                    <div class="modal-header bg-gradient-primary text-white border-0">
                        <h5 class="modal-title fw-bold" id="jobProgressModalLabel">
                            <i class="fas fa-rocket me-2"></i>
                            Running Job: <span id="job-name">Loading...</span>
                        </h5>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-white text-primary me-2" id="job-status">Initializing</span>
                        </div>
                    </div>
                    <div class="modal-body p-4">
                        <!-- Main Progress Bar -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-semibold text-muted">Overall Progress</span>
                                <span class="badge bg-primary" id="progress-percentage">0%</span>
                            </div>
                            <div class="progress" style="height: 12px; border-radius: 10px;">
                                <div id="main-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-gradient-primary"
                                     role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                        </div>

                        <!-- Statistics Cards -->
                        <div class="row g-3 mb-4">
                            <div class="col-6 col-md-3">
                                <div class="card border-0 bg-primary bg-opacity-10 text-center">
                                    <div class="card-body py-3">
                                        <div class="h4 mb-1 text-primary fw-bold" id="processed-count">0</div>
                                        <div class="small text-muted fw-semibold">Processed</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="card border-0 bg-success bg-opacity-10 text-center">
                                    <div class="card-body py-3">
                                        <div class="h4 mb-1 text-success fw-bold" id="new-count">0</div>
                                        <div class="small text-muted fw-semibold">New Posts</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="card border-0 bg-warning bg-opacity-10 text-center">
                                    <div class="card-body py-3">
                                        <div class="h4 mb-1 text-warning fw-bold" id="updated-count">0</div>
                                        <div class="small text-muted fw-semibold">Updated</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="card border-0 bg-secondary bg-opacity-10 text-center">
                                    <div class="card-body py-3">
                                        <div class="h4 mb-1 text-secondary fw-bold" id="skipped-count">0</div>
                                        <div class="small text-muted fw-semibold">Skipped</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Current Activity -->
                        <div class="card border-0 bg-light mb-4" id="current-activity" style="display: none;">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="spinner-border spinner-border-sm text-primary me-3" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div>
                                        <div class="fw-semibold text-primary">Currently Processing</div>
                                        <div class="text-muted" id="current-post-title">Initializing...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Live Log -->
                        <div class="card border-0 bg-dark">
                            <div class="card-header bg-dark border-0 py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0 text-white">
                                        <i class="fas fa-terminal me-2"></i>Live Log
                                    </h6>
                                    <button class="btn btn-sm btn-outline-light" id="clear-log">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body bg-dark p-3">
                                <pre id="job-log" class="text-light mb-0" style="max-height: 150px; overflow-y: auto; font-size: 0.8rem; line-height: 1.4;"></pre>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer border-0 bg-light">
                        <div class="d-flex justify-content-between w-100 align-items-center">
                            <div class="text-muted small">
                                <i class="fas fa-clock me-1"></i>
                                Started: <span id="start-time">--</span>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-secondary" id="minimize-modal">
                                    <i class="fas fa-window-minimize me-1"></i> Minimize
                                </button>
                                <button type="button" class="btn btn-secondary" id="cancel-job" style="display: none;">
                                    <i class="fas fa-stop me-1"></i> Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Minimized Progress Indicator -->
        <div id="minimized-progress" class="position-fixed bottom-0 end-0 m-4" style="display: none; z-index: 1055;">
            <div class="card border-0 shadow-lg" style="width: 300px;">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="fw-semibold">Job Running</div>
                        <button class="btn btn-sm btn-outline-primary" id="restore-modal">
                            <i class="fas fa-window-restore"></i>
                        </button>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div id="mini-progress-bar" class="progress-bar bg-primary" style="width: 0%"></div>
                    </div>
                    <div class="small text-muted mt-1" id="mini-status">Processing...</div>
                </div>
            </div>
        </div>
        `;

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get modal instance
        this.modal = new bootstrap.Modal(document.getElementById('jobProgressModal'));

        // Setup event listeners
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Clear log button
        document.getElementById('clear-log').addEventListener('click', () => {
            document.getElementById('job-log').textContent = '';
        });

        // Minimize modal
        document.getElementById('minimize-modal').addEventListener('click', () => {
            this.minimizeModal();
        });

        // Restore modal
        document.getElementById('restore-modal').addEventListener('click', () => {
            this.restoreModal();
        });

        // Handle modal close
        document.getElementById('jobProgressModal').addEventListener('hidden.bs.modal', () => {
            if (this.isRunning) {
                this.showMinimized();
            }
        });
    }

    startJob(jobId, jobName) {
        this.jobId = jobId;
        this.isRunning = true;

        // Set job name
        document.getElementById('job-name').textContent = jobName;
        document.getElementById('start-time').textContent = new Date().toLocaleTimeString();

        // Reset progress
        this.resetProgress();

        // Show modal
        this.modal.show();

        // Start progress monitoring
        this.startProgressMonitoring();

        // Start the actual job
        this.executeJob();
    }

    executeJob() {
        // Add initial log entry
        this.addLogEntry('Starting job execution...');

        // Get the base URL
        const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '');

        // Execute job via fetch
        fetch(`${baseUrl}/api/run_job.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                job_id: this.jobId
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                this.addLogEntry('Job completed successfully!');
                this.completeJob(data);
            } else {
                this.addLogEntry('Job failed: ' + (data.error || 'Unknown error'));
                this.failJob(data.error);
            }
        })
        .catch(error => {
            this.addLogEntry('Job failed: ' + error.message);
            this.failJob(error.message);
        });
    }

    startProgressMonitoring() {
        this.progressInterval = setInterval(() => {
            this.updateProgress();
        }, 1000);
    }

    updateProgress() {
        if (!this.isRunning) return;

        const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '');

        fetch(`${baseUrl}/get_job_progress.php?job_id=${this.jobId}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'running') {
                    this.updateProgressDisplay(data.progress || {});
                } else if (data.status === 'completed') {
                    this.completeJob(data);
                } else if (data.status === 'failed') {
                    this.failJob(data.error);
                }
            })
            .catch(error => {
                console.error('Error fetching progress:', error);
            });
    }

    updateProgressDisplay(progress) {
        const percentage = progress.progress || 0;

        // Update main progress bar
        const progressBar = document.getElementById('main-progress-bar');
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
        document.getElementById('progress-percentage').textContent = percentage + '%';

        // Update mini progress bar
        document.getElementById('mini-progress-bar').style.width = percentage + '%';

        // Update counters
        document.getElementById('processed-count').textContent = progress.processed || 0;
        document.getElementById('new-count').textContent = progress.new || 0;
        document.getElementById('updated-count').textContent = progress.updated || 0;
        document.getElementById('skipped-count').textContent = progress.skipped || 0;

        // Update current activity
        if (progress.post_title) {
            document.getElementById('current-activity').style.display = 'block';
            document.getElementById('current-post-title').textContent = progress.post_title;
            document.getElementById('mini-status').textContent = 'Processing: ' + progress.post_title;

            // Add to log
            this.addLogEntry('Processing: ' + progress.post_title);
        }

        // Update status
        document.getElementById('job-status').textContent = 'Running';
    }

    addLogEntry(message) {
        const logElement = document.getElementById('job-log');
        const timestamp = new Date().toLocaleTimeString();
        logElement.textContent += `[${timestamp}] ${message}\n`;
        logElement.scrollTop = logElement.scrollHeight;
    }

    completeJob(data) {
        this.isRunning = false;
        clearInterval(this.progressInterval);

        // Update status
        document.getElementById('job-status').textContent = 'Completed';
        document.getElementById('job-status').className = 'badge bg-success text-white me-2';

        // Hide current activity
        document.getElementById('current-activity').style.display = 'none';

        // Add completion log
        this.addLogEntry('Job completed successfully!');

        // Show completion notification
        this.showCompletionNotification(data);

        // Auto-close after 3 seconds
        setTimeout(() => {
            this.closeModal();
            // Refresh the page to show new posts
            window.location.reload();
        }, 3000);
    }

    failJob(error) {
        this.isRunning = false;
        clearInterval(this.progressInterval);

        // Update status
        document.getElementById('job-status').textContent = 'Failed';
        document.getElementById('job-status').className = 'badge bg-danger text-white me-2';

        // Hide current activity
        document.getElementById('current-activity').style.display = 'none';

        // Add error log
        this.addLogEntry('Job failed: ' + error);

        // Show error notification
        showNotification('Job failed: ' + error, 'error', 5000);
    }

    minimizeModal() {
        this.modal.hide();
    }

    restoreModal() {
        document.getElementById('minimized-progress').style.display = 'none';
        this.modal.show();
    }

    showMinimized() {
        document.getElementById('minimized-progress').style.display = 'block';
    }

    closeModal() {
        this.modal.hide();
        document.getElementById('minimized-progress').style.display = 'none';
    }

    resetProgress() {
        // Reset all progress indicators
        document.getElementById('main-progress-bar').style.width = '0%';
        document.getElementById('mini-progress-bar').style.width = '0%';
        document.getElementById('progress-percentage').textContent = '0%';

        // Reset counters
        document.getElementById('processed-count').textContent = '0';
        document.getElementById('new-count').textContent = '0';
        document.getElementById('updated-count').textContent = '0';
        document.getElementById('skipped-count').textContent = '0';

        // Reset status
        document.getElementById('job-status').textContent = 'Initializing';
        document.getElementById('job-status').className = 'badge bg-warning text-dark me-2';

        // Clear log
        document.getElementById('job-log').textContent = '';

        // Hide current activity
        document.getElementById('current-activity').style.display = 'none';
    }

    showCompletionNotification(data) {
        const stats = data.stats || {};
        const message = `Job completed! Processed ${stats.processed || 0} posts, ${stats.new || 0} new, ${stats.updated || 0} updated.`;
        showNotification(message, 'success', 5000);
    }
}

// Initialize the progress modal system
let jobProgressModal;
document.addEventListener('DOMContentLoaded', function() {
    jobProgressModal = new JobProgressModal();
});

// Global function to start a job with progress
window.startJobWithProgress = function(jobId, jobName) {
    if (jobProgressModal) {
        jobProgressModal.startJob(jobId, jobName);
    }
};
