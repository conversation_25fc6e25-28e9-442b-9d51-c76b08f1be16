<div class="text-center mb-4">
    <i class="fas fa-globe fa-4x text-primary mb-3"></i>
    <h2>Welcome to Content Grabber</h2>
    <p class="lead">A powerful tool for grabbing content from WordPress sites and sitemaps</p>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">Features</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        WordPress REST API Support
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Sitemap Content Grabbing
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Automatic Content Detection
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Image Downloading
                    </li>
                </ul>
            </div>
            <div class="col-md-6">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        HTML & PDF Generation
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Job Scheduling
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Content Filtering
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Beautiful Content Display
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">Requirements</h5>
    </div>
    <div class="card-body">
        <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex justify-content-between align-items-center">
                PHP Version 7.4+
                <span class="badge bg-<?php echo PHP_VERSION_ID >= 70400 ? 'success' : 'danger'; ?>">
                    <?php echo PHP_VERSION_ID >= 70400 ? 'Passed' : 'Failed'; ?>
                </span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
                PDO Extension
                <span class="badge bg-<?php echo extension_loaded('pdo') ? 'success' : 'danger'; ?>">
                    <?php echo extension_loaded('pdo') ? 'Passed' : 'Failed'; ?>
                </span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
                cURL Extension
                <span class="badge bg-<?php echo extension_loaded('curl') ? 'success' : 'danger'; ?>">
                    <?php echo extension_loaded('curl') ? 'Passed' : 'Failed'; ?>
                </span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
                DOM Extension
                <span class="badge bg-<?php echo extension_loaded('dom') ? 'success' : 'danger'; ?>">
                    <?php echo extension_loaded('dom') ? 'Passed' : 'Failed'; ?>
                </span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
                Writable Directory
                <span class="badge bg-<?php echo is_writable(BASE_PATH) ? 'success' : 'danger'; ?>">
                    <?php echo is_writable(BASE_PATH) ? 'Passed' : 'Failed'; ?>
                </span>
            </li>
        </ul>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i>
    Click the "Next" button to continue with the setup process.
</div>
