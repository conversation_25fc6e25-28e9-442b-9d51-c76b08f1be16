<?php
/**
 * Test Enhanced Sitemap Grabber
 * 
 * This script tests the EnhancedSitemapGrabber class on any site with a sitemap.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Create content directories
create_content_directories();

// Load required files
require_once 'includes/Database.php';
require_once 'includes/ContentGrabber.php';
require_once 'includes/EnhancedSitemapGrabber.php';

// Initialize database connection
$db = new Database();

// URL to test
$url = isset($_GET['url']) ? $_GET['url'] : 'https://www.sitemaps.org/sitemap.xml';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 5;
$debug = true;
$raw_mode = isset($_GET['raw_mode']) ? (bool)$_GET['raw_mode'] : false;

// HTML header
if (!$raw_mode) {
    echo '<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Test Enhanced Sitemap Grabber</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <style>
            pre {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                overflow: auto;
                max-height: 400px;
            }
            .success {
                color: green;
                font-weight: bold;
            }
            .error {
                color: red;
                font-weight: bold;
            }
            .warning {
                color: orange;
                font-weight: bold;
            }
            .info {
                color: blue;
            }
            .code {
                font-family: monospace;
                background-color: #f0f0f0;
                padding: 2px 4px;
                border-radius: 3px;
            }
        </style>
    </head>
    <body>
        <div class="container py-5">
            <h1>Test Enhanced Sitemap Grabber</h1>
            <p class="lead">This tool tests the EnhancedSitemapGrabber class on any site with a sitemap.</p>
            
            <form method="get" class="mb-4">
                <div class="mb-3">
                    <label for="url" class="form-label">Sitemap URL</label>
                    <input type="url" class="form-control" id="url" name="url" value="' . htmlspecialchars($url) . '" required>
                </div>
                <div class="mb-3">
                    <label for="limit" class="form-label">Number of posts to grab</label>
                    <input type="number" class="form-control" id="limit" name="limit" value="' . $limit . '" min="1" max="100">
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="raw_mode" name="raw_mode" value="1" ' . ($raw_mode ? 'checked' : '') . '>
                    <label class="form-check-label" for="raw_mode">Raw mode (no HTML)</label>
                </div>
                <button type="submit" class="btn btn-primary">Test Grabber</button>
            </form>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Test Results</h5>
                </div>
                <div class="card-body">
                    <pre>';
}

// Start output buffer for raw mode
if ($raw_mode) {
    ob_start();
}

echo "Testing Enhanced Sitemap Grabber\n";
echo "URL: $url\n";
echo "Limit: $limit posts\n";
echo "Debug mode: Enabled\n\n";

// Create a custom error handler to capture all errors
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    echo "<span class='error'>PHP ERROR [$errno]: $errstr in $errfile on line $errline</span>\n";
    return true;
});

// Create a subclass of EnhancedSitemapGrabber to expose protected methods
class DebugEnhancedSitemapGrabber extends EnhancedSitemapGrabber {
    public function publicDirectFetch($url) {
        return $this->directFetch($url);
    }
    
    public function publicExtractUrlsFromSitemap($url) {
        return $this->extractUrlsFromSitemap($url);
    }
    
    public function publicProcessUrl($url) {
        return $this->processUrl($url);
    }
}

try {
    // First, check if the URL is a sitemap
    echo "<span class='info'>Checking if $url is a sitemap...</span>\n";
    $isSitemap = ContentGrabber::isSitemap($url, ['debug' => $debug]);
    
    if ($isSitemap) {
        echo "<span class='success'>$url is a sitemap!</span>\n\n";
    } else {
        echo "<span class='error'>$url does not appear to be a sitemap!</span>\n";
        echo "Please enter a valid sitemap URL.\n\n";
        throw new Exception("Not a sitemap");
    }
    
    // Create grabber with debug mode
    echo "<span class='info'>Creating EnhancedSitemapGrabber...</span>\n";
    $options = [
        'debug' => $debug
    ];
    
    // Create grabber
    $grabber = new DebugEnhancedSitemapGrabber($db, $url, $options);
    
    // Test direct fetch
    echo "<span class='info'>Testing direct fetch...</span>\n";
    $response = $grabber->publicDirectFetch($url);
    
    if ($response) {
        echo "<span class='success'>Direct fetch successful!</span>\n";
        echo "Response length: " . strlen($response) . " bytes\n\n";
    } else {
        echo "<span class='error'>Direct fetch failed!</span>\n\n";
    }
    
    // Test extracting URLs from sitemap
    echo "<span class='info'>Extracting URLs from sitemap...</span>\n";
    $urls = $grabber->publicExtractUrlsFromSitemap($url);
    
    if (!empty($urls)) {
        echo "<span class='success'>Found " . count($urls) . " URLs in sitemap!</span>\n\n";
        
        // Display first 5 URLs
        echo "First " . min(5, count($urls)) . " URLs:\n";
        for ($i = 0; $i < min(5, count($urls)); $i++) {
            echo "  " . ($i + 1) . ". " . $urls[$i]['loc'] . " (Last modified: " . date('Y-m-d H:i:s', $urls[$i]['lastmod']) . ")\n";
        }
        echo "\n";
        
        // Test processing a URL
        if (!empty($urls)) {
            echo "<span class='info'>Testing URL processing...</span>\n";
            try {
                $post = $grabber->publicProcessUrl($urls[0]['loc']);
                if ($post) {
                    echo "<span class='success'>URL processed successfully!</span>\n";
                    echo "Processed URL: " . $urls[0]['loc'] . "\n";
                    echo "Title: " . $post['title'] . "\n";
                    echo "Date: " . $post['date'] . "\n";
                    echo "Content length: " . strlen($post['content']) . " bytes\n";
                    echo "Images: " . count($post['images']) . "\n\n";
                } else {
                    echo "<span class='error'>URL processing failed!</span>\n\n";
                }
            } catch (Exception $e) {
                echo "<span class='error'>Error processing URL: " . $e->getMessage() . "</span>\n\n";
            }
        }
    } else {
        echo "<span class='error'>No URLs found in sitemap!</span>\n\n";
    }
    
    // Grab posts
    echo "<span class='info'>Grabbing $limit posts...</span>\n";
    $startTime = microtime(true);
    $posts = $grabber->grab($limit);
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);
    
    if (empty($posts)) {
        echo "<span class='error'>No posts grabbed!</span>\n";
        echo "Execution time: $executionTime seconds\n\n";
        
        // Check error log
        echo "<span class='info'>Checking error log...</span>\n";
        $errorLog = error_get_last();
        if ($errorLog) {
            echo "Last error: " . $errorLog['message'] . " in " . $errorLog['file'] . " on line " . $errorLog['line'] . "\n\n";
        }
        
        // Suggest fixes
        echo "<span class='info'>Suggestions:</span>\n";
        echo "1. Check if the sitemap is accessible\n";
        echo "2. Verify that the sitemap contains valid URLs\n";
        echo "3. Check for any PHP errors in the logs\n";
    } else {
        echo "<span class='success'>Successfully grabbed " . count($posts) . " posts in $executionTime seconds!</span>\n\n";
        
        // Display post details
        echo "<strong>Post details:</strong>\n";
        echo "------------\n";
        foreach ($posts as $index => $post) {
            echo "Post #" . ($index + 1) . ":\n";
            echo "  ID: " . $post['id'] . "\n";
            echo "  Title: " . $post['title'] . "\n";
            echo "  Date: " . $post['date'] . "\n";
            echo "  Link: " . $post['link'] . "\n";
            echo "  Content length: " . strlen($post['content']) . " bytes\n";
            echo "  Images: " . count($post['images']) . "\n";
            echo "  Tags: " . count($post['tags']) . "\n";
            echo "\n";
        }
    }
} catch (Exception $e) {
    echo "<span class='error'>Error: " . $e->getMessage() . "</span>\n";
    echo "File: " . $e->getFile() . " on line " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Restore error handler
restore_error_handler();

// End output buffer for raw mode
if ($raw_mode) {
    $output = ob_get_clean();
    header('Content-Type: text/plain');
    echo $output;
    exit;
}

// HTML footer
echo '</pre>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="settings.php?page=settings" class="btn btn-secondary">Back to Settings</a>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
?>
