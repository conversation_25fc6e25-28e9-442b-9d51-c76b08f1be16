<?php
/**
 * Update Script - Add job_runs table
 * 
 * This script adds the job_runs table to the database.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Check if the application is installed
if (!is_installed()) {
    echo "Application not installed. Please run the setup wizard first.\n";
    exit(1);
}

// Load required files
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Update - Add job_runs Table</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container py-5">
        <h1>Database Update - Add job_runs Table</h1>';

// Check if the job_runs table already exists
$tableExists = false;
try {
    $db->query("SELECT 1 FROM job_runs LIMIT 1");
    $tableExists = true;
    
    echo '<div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        The job_runs table already exists. No update needed.
    </div>';
} catch (Exception $e) {
    // Table doesn't exist, we'll create it
    echo '<div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        The job_runs table does not exist. This update will create it.
    </div>';
}

// If the table doesn't exist, create it
if (!$tableExists) {
    if (isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
        try {
            // Start transaction
            $db->beginTransaction();
            
            // Create job_runs table
            $db->query("
                CREATE TABLE job_runs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    job_id INT NOT NULL,
                    status ENUM('pending', 'running', 'completed', 'failed') NOT NULL DEFAULT 'pending',
                    start_time DATETIME NOT NULL,
                    end_time DATETIME NULL,
                    posts_grabbed INT NULL DEFAULT 0,
                    error TEXT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE
                )
            ");
            
            // Commit transaction
            $db->commit();
            
            echo '<div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                The job_runs table has been created successfully!
            </div>';
        } catch (Exception $e) {
            // Rollback transaction
            $db->rollback();
            
            echo '<div class="alert alert-danger">
                <i class="fas fa-times-circle me-2"></i>
                Error creating job_runs table: ' . htmlspecialchars($e->getMessage()) . '
            </div>';
        }
    } else {
        // Show confirmation form
        echo '<div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Confirm Update</h5>
            </div>
            <div class="card-body">
                <p>This update will add a new table to track job runs:</p>
                <ul>
                    <li><strong>job_runs</strong> - Tracks each time a job is run, including start time, end time, and results</li>
                </ul>
                <p>This will enable better tracking and debugging of job execution.</p>
                
                <form method="post">
                    <input type="hidden" name="confirm" value="yes">
                    <button type="submit" class="btn btn-primary">Run Update</button>
                    <a href="' . BASE_URL . '/" class="btn btn-secondary ms-2">Cancel</a>
                </form>
            </div>
        </div>';
    }
}

// Add links to other tools
echo '<div class="mt-4">
    <h4>Other Tools</h4>
    <div class="list-group">
        <a href="debug_job.php" class="list-group-item list-group-item-action">
            <i class="fas fa-bug me-2"></i> Job Debugger
        </a>
        <a href="test_url.php" class="list-group-item list-group-item-action">
            <i class="fas fa-link me-2"></i> URL Test Tool
        </a>
        <a href="' . BASE_URL . '/?page=jobs" class="list-group-item list-group-item-action">
            <i class="fas fa-tasks me-2"></i> Back to Jobs
        </a>
    </div>
</div>';

echo '</div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</body>
</html>';
?>
