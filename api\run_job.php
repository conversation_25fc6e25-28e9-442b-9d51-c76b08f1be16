<?php
/**
 * Run Job API Endpoint
 *
 * This script runs a job via AJAX and returns JSON response.
 */

// Start session
session_start();

// Disable error display for clean JSON output
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

// Set content type to JSON
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!isset($input['job_id'])) {
        throw new Exception('Job ID is required');
    }

    $jobId = (int)$input['job_id'];

    // Redirect to the existing job run system
    $redirectUrl = '../?page=jobs&action=run&id=' . $jobId;

    // Since we can't redirect in AJAX, we'll simulate the job run
    // Load configuration
    require_once '../config.php';

    // Load required files
    require_once '../includes/Database.php';
    require_once '../includes/JobManager.php';
    require_once '../includes/ContentGrabber.php';

    // Initialize database and job manager
    $db = new Database();
    $jobManager = new JobManager($db);

    // Get job details
    $job = $jobManager->getJob($jobId);
    if (!$job) {
        throw new Exception('Job not found');
    }

    // Run the job
    $result = $jobManager->runJob($jobId);

    if ($result['success']) {
        // Store stats in session for popup
        $_SESSION['job_stats'] = $result['stats'] ?? null;
        $_SESSION['job_id'] = $jobId;
        $_SESSION['job_posts_count'] = $result['posts_count'] ?? 0;

        echo json_encode([
            'success' => true,
            'message' => 'Job completed successfully',
            'stats' => $result['stats'] ?? [],
            'posts_count' => $result['posts_count'] ?? 0
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => $result['error'] ?? 'Unknown error occurred'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Fatal error: ' . $e->getMessage()
    ]);
}
?>
