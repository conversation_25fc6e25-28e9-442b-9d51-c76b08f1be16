<div class="text-center mb-4">
    <i class="fas fa-database fa-4x text-primary mb-3"></i>
    <h2>Database Setup</h2>
    <p class="lead">Configure your database connection</p>
</div>

<form method="post" action="?step=2">
    <div class="mb-3">
        <label for="db_host" class="form-label">Database Host</label>
        <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
        <div class="form-text">Usually "localhost" or "127.0.0.1"</div>
    </div>
    
    <div class="mb-3">
        <label for="db_name" class="form-label">Database Name</label>
        <input type="text" class="form-control" id="db_name" name="db_name" value="content_grabber" required>
        <div class="form-text">The database will be created if it doesn't exist</div>
    </div>
    
    <div class="mb-3">
        <label for="db_user" class="form-label">Database Username</label>
        <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
    </div>
    
    <div class="mb-3">
        <label for="db_pass" class="form-label">Database Password</label>
        <input type="password" class="form-control" id="db_pass" name="db_pass">
        <div class="form-text">Leave empty if your database has no password</div>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        The setup will create all necessary tables for the application.
    </div>
    
    <div class="d-grid">
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-2"></i> Save Database Settings
        </button>
    </div>
</form>
